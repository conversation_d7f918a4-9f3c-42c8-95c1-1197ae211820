/*
 * File: ArcAdas_EmbeddedCoder_Frame_private.h
 *
 * Code generated for Simulink model 'ArcAdas_EmbeddedCoder_Frame'.
 *
 * Model version                  : 7.2225
 * Simulink Coder version         : 9.6 (R2021b) 14-May-2021
 * C/C++ source code generated on : Thu Jul  3 17:31:36 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Custom Processor
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_ArcAdas_EmbeddedCoder_Frame_private_h_
#define RTW_HEADER_ArcAdas_EmbeddedCoder_Frame_private_h_
#include "rtwtypes.h"
#include "ArcAdas_EmbeddedCoder_Frame.h"
#ifndef UCHAR_MAX
#include <limits.h>
#endif

#if ( UCHAR_MAX != (0xFFU) ) || ( SCHAR_MAX != (0x7F) )
#error Code was generated for compiler with different sized uchar/char. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( USHRT_MAX != (0xFFFFU) ) || ( SHRT_MAX != (0x7FFF) )
#error Code was generated for compiler with different sized ushort/short. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( UINT_MAX != (0xFFFFFFFFU) ) || ( INT_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized uint/int. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( ULONG_MAX != (0xFFFFFFFFU) ) || ( LONG_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized ulong/long. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

/* Skipping ulong_long/long_long check: insufficient preprocessor integer range. */
extern real32_T rt_powf_snf(real32_T u0, real32_T u1);
extern real_T rt_powd_snf(real_T u0, real_T u1);
extern void rt_mrdivided4x4_snf(const real_T u0[16], const real_T u1[16], real_T
  y[16]);
extern real32_T look1_iflf_binlcpw(real32_T u0, const real32_T bp0[], const
  real32_T table[], uint32_T maxIndex);
extern real32_T look2_iflf_binlgpw(real32_T u0, real32_T u1, const real32_T bp0[],
  const real32_T bp1[], const real32_T table[], const uint32_T maxIndex[],
  uint32_T stride);
extern uint32_T plook_u32fdd_binca(real32_T u, const real_T bp[], uint32_T
  maxIndex, real_T *fraction);
extern uint32_T binsearch_u32d(real_T u, const real_T bp[], uint32_T startIndex,
  uint32_T maxIndex);
extern uint32_T binsearch_u32f(real32_T u, const real32_T bp[], uint32_T
  startIndex, uint32_T maxIndex);
extern real_T look1_binlxpw(real_T u0, const real_T bp0[], const real_T table[],
  uint32_T maxIndex);
extern real32_T look1_iflf_binlxpw(real32_T u0, const real32_T bp0[], const
  real32_T table[], uint32_T maxIndex);
extern real32_T look2_iflf_binfcapw(real32_T u0, real32_T u1, const real32_T
  bp0[], const real32_T bp1[], const real32_T table[], const uint32_T maxIndex[],
  uint32_T stride);
extern real32_T look2_iflf_binlcpw(real32_T u0, real32_T u1, const real32_T bp0[],
  const real32_T bp1[], const real32_T table[], const uint32_T maxIndex[],
  uint32_T stride);
extern real32_T look2_iflf_binlcapw(real32_T u0, real32_T u1, const real32_T
  bp0[], const real32_T bp1[], const real32_T table[], const uint32_T maxIndex[],
  uint32_T stride);
extern real32_T look2_iflf_binlxpw(real32_T u0, real32_T u1, const real32_T bp0[],
  const real32_T bp1[], const real32_T table[], const uint32_T maxIndex[],
  uint32_T stride);
extern real_T look1_linlcpw(real_T u0, const real_T bp0[], const real_T table[],
  uint32_T maxIndex);
extern real_T look2_linlcpw(real_T u0, real_T u1, const real_T bp0[], const
  real_T bp1[], const real_T table[], const uint32_T maxIndex[], uint32_T stride);
extern uint32_T plook_u32u8_bincka(uint8_T u, const uint8_T bp[], uint32_T
  maxIndex);
extern uint32_T plook_u32f_bincka(real32_T u, const real32_T bp[], uint32_T
  maxIndex);
extern uint32_T binsearch_u32u8(uint8_T u, const uint8_T bp[], uint32_T
  startIndex, uint32_T maxIndex);
extern void DetectionOfPressDuration_M_Init(boolean_T *rty_Switch_Short,
  boolean_T *rty_Switch_Long);
extern void DetectionOfPressDuration_Minus(uint8_T rtu_Switch, boolean_T
  *rty_Switch_Short, boolean_T *rty_Switch_Long, DW_DetectionOfPressDuration_M_T
  *localDW);
extern void ArcAdas_Embedded_MATLABFunction(uint64_T rtu_Raw_Obj_Id, real32_T
  rtu_dt, real32_T rtu_confirmTime, uint64_T *rty_Confirmed_Obj_Id,
  DW_MATLABFunction_ArcAdas_Emb_T *localDW);
extern void ArcAdas_Embe_SlidingSurface_Cal(real32_T rtu_alpha_1, real32_T
  rtu_alpha_1_h, real32_T rtu_alpha_1_e, real32_T rtu_alpha_1_k, real32_T
  rtu_alpha_1_eh, real32_T rtu_alpha_2, real32_T rtu_alpha_2_k, real32_T
  rtu_alpha_2_e, real32_T rtu_alpha_2_j, real32_T rtu_alpha_2_j4, real32_T
  rtu_miu_2, real32_T rtu_miu_2_m, real32_T rtu_miu_2_c, real32_T rtu_miu_2_n,
  real32_T rtu_miu_2_d, real32_T rtu_range, real32_T rtu_rangerate, real32_T
  rtu_d_ref, real32_T rtu_liftedValue, real32_T rtu_liftedValue_d, real32_T
  rtu_liftedValue_b, real32_T rtu_liftedValue_bd, real32_T rtu_liftedValue_o,
  real32_T rty_Sigma[5], boolean_T *rty_Sigma1IsNegtive);
extern void ArcAdas_Embedd_MATLABFunction_p(real32_T rtu_RR, real32_T rtu_R,
  real32_T rtu_FOZoneHeight, real32_T rtu_FOZoneWidth, real32_T
  rtu_FOBlendHeight, real32_T rtu_FOBlendWidth, real32_T rtu_d_ref, real32_T
  *rty_P_cu, real32_T *rty_P_acu, real32_T *rty_P_os, real32_T *rty_P_fb,
  real32_T *rty_P_fo);
extern void ArcAdas_Embedd_MATLABFunction_l(real32_T rtu_proportion_cu, real32_T
  rtu_proportion_acu, real32_T rtu_proportion_os, real32_T rtu_proportion_fb,
  real32_T *rty_percentage_cu, real32_T *rty_percentage_acu, real32_T
  *rty_percentage_os, real32_T *rty_percentage_fb);
extern void ArcAdas_Embedd_MATLABFunction_f(real32_T rtu_range, real32_T
  rtu_d_ref, real32_T rtu_d_ref_blendzone_width, real32_T *rty_proportion_acu_d);
extern void ArcAdas_Embedde_MATLABFunction1(real32_T rtu_rangerate, real32_T
  rtu_v_catchup_acu_edge, real32_T rtu_v_catchup_blendzone_width, real32_T
  *rty_proportion_acu_v);
extern void ArcAdas_Embedd_MATLABFunction_a(real32_T rtu_range, real32_T
  rtu_d_ref, real32_T rtu_d_ref_blendzone_width, real32_T *rty_proportion_cu_d);
extern void ArcAdas_Embed_MATLABFunction1_h(real32_T rtu_rangerate, real32_T
  rtu_v_catchup_acu_edge, real32_T rtu_v_catchup_blendzone_width, real32_T
  *rty_proportion_cu_v);
extern void ArcAdas_Embedd_MATLABFunction_m(real32_T rtu_range, real32_T
  rtu_d_ref, real32_T rtu_d_ref_blendzone_width, real32_T *rty_proportion_fb_d);
extern void ArcAdas_Embed_MATLABFunction1_l(real32_T rtu_rangerate, real32_T
  rtu_minus_v_fallback, real32_T rtu_v_fallback_blendzone_width, real32_T
  *rty_proportion_fb_v);
extern void ArcAdas_Embedd_MATLABFunction_g(real32_T rtu_range, real32_T
  rtu_d_ref, real32_T rtu_d_ref_blendzone_width, real32_T *rty_proportion_os_d);
extern void ArcAdas_Embed_MATLABFunction1_d(real32_T rtu_rangerate, real32_T
  rtu_v_fallback, real32_T rtu_v_fallback_blendzone_width, real32_T
  *rty_proportion_os_v);
extern void ArcAdas_Embed_MATLABFunction1_i(real32_T rtu_range, real32_T
  rtu_a_lead, real32_T rtu_a_host, real32_T rtu_v_lead, real32_T rtu_v_host,
  boolean_T rtu_IsTargetChanged, boolean_T rtu_IsTargetPresent, const real32_T
  rtu_R[2], const real32_T rtu_Q[2], real32_T *rty_R_filtered, real32_T
  *rty_RR_filtered, real32_T *rty_v_lead_filtered,
  DW_MATLABFunction1_ArcAdas__h_T *localDW);
extern void ArcAdas_v_lead_average_smoother(real32_T rtu_v_lead, real32_T
  rtu_range, real32_T rtu_DistThdMid, real32_T rtu_DistThdFar, uint64_T
  rtu_Obj_id, real32_T *rty_v_lead_processed, DW_v_lead_average_smoother_Ar_T
  *localDW);
extern void ArcAdas_EmbeddedCod_control_law(real32_T rtu_alpha_1, real32_T
  rtu_alpha_1_h, real32_T rtu_alpha_1_b, real32_T rtu_alpha_1_o, real32_T
  rtu_alpha_1_a, real32_T rtu_alpha_2, real32_T rtu_alpha_2_b, real32_T
  rtu_alpha_2_h, real32_T rtu_alpha_2_f, real32_T rtu_alpha_2_e, real32_T
  rtu_alpha_3, real32_T rtu_alpha_3_j, real32_T rtu_alpha_3_l, real32_T
  rtu_alpha_3_a, real32_T rtu_alpha_3_jg, const real32_T rtu_Sigma[5], real32_T
  rtu_Sigma_fac, real32_T rtu_Sigma_fac_a, real32_T rtu_Sigma_fac_af, real32_T
  rtu_Sigma_fac_l, real32_T rtu_Sigma_fac_g, real32_T rtu_rangerate, real32_T
  rtu_a_lead, real32_T rtu_miu, real32_T rtu_miu_a, real32_T rtu_miu_o, real32_T
  rtu_miu_j, real32_T rtu_miu_n, real32_T rty_u[5]);
extern void ArcAdas_Embed_MATLABFunction_f2(real32_T rtu_blend_u_in, real32_T
  rtu_min_limit, real32_T rtu_max_limit, real32_T *rty_blend_u_out);
extern real32_T ArcAdas_Emb_SlidingM_LeftTarget(real32_T rtu_range, real32_T
  rtu_RangeRate, real32_T rtu_v_host, real32_T rtu_v_lead, real32_T rtu_a_lead,
  real32_T rtu_d_ref, real32_T rtu_Ref_TG, real32_T rtu_Actual_TG, boolean_T
  rtu_no_brake_target, const ConstB_SlidingM_LeftTarget_Ar_T *localC,
  DW_SlidingM_LeftTarget_ArcAda_T *localDW);
extern void ArcAdas_Embedded_PedestrianPred(boolean_T rtu_RstFlg, real_T
  rtu_FCP_AEB_ObjAbsV, real_T rtu_ObjCourseAgCos, real_T rtu_ObjCourseAgSin,
  real_T rtu_FCP_AEB_ObjY, real_T *rty_ObjVPred, real_T *rty_ObjXPred, real_T
  *rty_ObjYPred, real_T *rty_ObjAbsAPred, B_PedestrianPred_ArcAdas_Embe_T
  *localB, const ConstB_PedestrianPred_ArcAdas_T *localC,
  DW_PedestrianPred_ArcAdas_Emb_T *localDW);
extern void ArcAdas_Embedd_MATLABFunction_o(real_T rtu_COI_AEB_ObjVXLongRel,
  real_T *rty_y);
extern void ArcAdas_Embed_IfActionSubsystem(real_T rtu_In1, real_T *rty_Out1,
  DW_IfActionSubsystem_ArcAdas__T *localDW);
extern void ArcAdas_EmbeddedCoder__NoObject(real_T *rty_CFG_AEB_AebTtcThd);
extern void ArcAdas_EmbeddedC_InvalidObject(real_T
  *rty_CFG_AEB_PredictArea_LBounda, real_T *rty_CFG_AEB_PredictArea_RBounda,
  real_T *rty_CFG_AEB_CurrentArea_LBounda, real_T
  *rty_CFG_AEB_CurrentArea_RBounda);
extern void ArcAdas_Embedd_PedestrianObject(real_T rtu_NegObjVXRel_Scal, real_T *
  rty_CFG_AEB_PredictArea_LBounda, real_T *rty_CFG_AEB_PredictArea_RBounda,
  real_T *rty_CFG_AEB_CurrentArea_LBounda, real_T
  *rty_CFG_AEB_CurrentArea_RBounda, const ConstB_PedestrianObject_ArcAd_T
  *localC);
extern void ArcAdas_Embe_PedestrianObject_g(real_T rtu_NegObjVXRel_Scal, real_T *
  rty_CFG_AEB_PredictArea_WidthIn, real_T *rty_CFG_AEB_CurrentArea_WidthIn);
extern void ArcAdas_Embe_IfActionSubsystem1(boolean_T
  *rty_CFG_AEB_LateralAEBAreaFlg);
extern void ArcAdas_Emb_IfActionSubsystem_p(boolean_T
  *rty_CFG_AEB_LateralAEBAreaFlg);
extern void ArcAdas_Emb_IfActionSubsystem_o(boolean_T
  *rty_CFG_AEB_LateralAEBCurrentAr);
extern void ArcAdas_Em_IfActionSubsystem1_m(boolean_T
  *rty_CFG_AEB_LateralAEBCurrentAr);
extern void ArcAdas_Embe_IfActionSubsystem2(boolean_T
  *rty_CFG_AEB_LateralAEBPredictAr);
extern void ArcAdas_Embe_IfActionSubsystem3(boolean_T
  *rty_CFG_AEB_LateralAEBPredictAr);
extern void ArcAdas_Embedded_CalcEgoVehMove(real32_T rtu_tCycle, real32_T
  rtu_Kap, real32_T rtu_Vego, real32_T *rty_dDeltaX, real32_T *rty_dDeltaY,
  real32_T *rty_CosOfPsi, real32_T *rty_SinOfPsi);

#endif                   /* RTW_HEADER_ArcAdas_EmbeddedCoder_Frame_private_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
