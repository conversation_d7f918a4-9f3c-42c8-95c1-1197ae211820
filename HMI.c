
#include "ArcAdas_EmbeddedCoder_Frame.h"
#include "ArcAdas_EmbeddedCoder_Frame_types.h"
#include <string.h>
#include <math.h>
#include "arcsoft_adas_connection_SL.h"

/*obj*/
uint8_t cc_Obj_ID[6] = {0};
float   cc_Obj_LongDistance[6] = {0};
float   cc_Obj_AbsLongVel[6] = {0};
float   cc_Obj_LatDistance[6] = {0};
uint8_t cc_Obj_ObjType[6] = {0};
float   cc_Obj_LatVel[6] = {0};  
float   cc_Obj_LongAccel[6] = {0};  
uint8_t cc_Obj_LocIndex[6] = {0}; 

/*LD HMI*/
uint8_t cc_LeftLaneColor = 0;
uint8_t cc_LeftLaneType = 0;
uint8_t cc_LeftLeftLaneColor = 0;
uint8_t cc_LeftLeftLaneType = 0;	
uint8_t cc_RightLaneColor = 0;
uint8_t cc_RightLaneType = 0;
uint8_t cc_RightRightLaneColor = 0;
uint8_t cc_RightRightLaneType = 0;
float   cc_LeftLane_C0 = 0;
float   cc_LeftLane_C1 = 0;
float   cc_LeftLane_C2 = 0;
float   cc_LeftLane_C3 = 0;
float   cc_LeftLeftLane_C0 = 0;
float   cc_LeftLeftLane_C1 = 0;
float   cc_LeftLeftLane_C2 = 0;
float   cc_LeftLeftLane_C3 = 0;	
float   cc_RightLane_C0 = 0;
float   cc_RightLane_C1 = 0;
float   cc_RightLane_C2 = 0;
float   cc_RightLane_C3 = 0;
float   cc_RightRightLane_C0 = 0;
float   cc_RightRightLane_C1 = 0;
float   cc_RightRightLane_C2 = 0;
float   cc_RightRightLane_C3 = 0;
uint8_t cc_LCC_Active = 0;

/*CAM CIPV INFO*/
uint8_t cc_CIPV_ID = 0;
float   cc_CIPV_LongDistance = 0;
float   cc_CIPV_AbsLongVel = 0;
float   cc_CIPV_LatDistance = 0;
uint8_t cc_CIPV_ObjType = 0;
float   cc_CIPV_LatVel = 0;  
float   cc_CIPV_LongAccel = 0;  
uint8_t cc_CIPV_LocIndex = 0; 

/*OBJ HMI*/
uint8_t cc_MainObjID = 0;
uint8_t cc_Obj1_Type = 0;
float   cc_Obj1_LatDis = 0;
float   cc_Obj1_LongDis = 0;
uint8_t cc_Obj2_Type = 0;
float   cc_Obj2_LatDis = 0;
float   cc_Obj2_LongDis = 0;
uint8_t cc_Obj2_Color = 0;
uint8_t cc_Obj3_Type = 0;
float   cc_Obj3_LatDis = 0;
float   cc_Obj3_LongDis = 0;
uint8_t cc_Obj3_Color = 0;
uint8_t cc_Obj4_Type = 0;
float   cc_Obj4_LatDis = 0;
float   cc_Obj4_LongDis = 0;
uint8_t cc_Obj4_Color = 0;
uint8_t cc_Obj5_Type = 0;
float   cc_Obj5_LatDis = 0;
float   cc_Obj5_LongDis = 0;
uint8_t cc_Obj5_Color = 0;
uint8_t cc_Obj6_Type = 0;
float   cc_Obj6_LatDis = 0;
float   cc_Obj6_LongDis = 0;
uint8_t cc_Obj7_Type = 0;
float   cc_Obj7_LatDis = 0;
float   cc_Obj7_LongDis = 0;
uint8_t cc_Obj8_Type = 0;
float   cc_Obj8_LatDis = 0;
float   cc_Obj8_LongDis = 0;
uint8_t cc_Obj9_Type = 0;
float   cc_Obj9_LatDis = 0;
float   cc_Obj9_LongDis = 0;
const float rearAxle2Front_Offset = 4;

void update_hmi_inputSignal(void)
{
	//OBJ_ID
    cc_Obj_ID[0] = ArcAdas_EmbeddedCoder_Frame_U.ACCObject_1.ObjectFusionID;
    cc_Obj_ID[1] = ArcAdas_EmbeddedCoder_Frame_U.ACCObject_2.ObjectFusionID;
    cc_Obj_ID[2] = ArcAdas_EmbeddedCoder_Frame_U.ACCObject_3.ObjectFusionID;
    cc_Obj_ID[3] = ArcAdas_EmbeddedCoder_Frame_U.ACCObject_4.ObjectFusionID;
    cc_Obj_ID[4] = ArcAdas_EmbeddedCoder_Frame_U.ACCObject_5.ObjectFusionID;
    cc_Obj_ID[5] = ArcAdas_EmbeddedCoder_Frame_U.ACCObject_6.ObjectFusionID;
    
	//OBJ_Abs_Lat_Velocity
	cc_Obj_LatVel[0] = ArcAdas_EmbeddedCoder_Frame_U.ACCObject_1.Lat_Vel;
	cc_Obj_LatVel[1] = ArcAdas_EmbeddedCoder_Frame_U.ACCObject_2.Lat_Vel;	
	cc_Obj_LatVel[2] = ArcAdas_EmbeddedCoder_Frame_U.ACCObject_3.Lat_Vel;	
	cc_Obj_LatVel[3] = ArcAdas_EmbeddedCoder_Frame_U.ACCObject_4.Lat_Vel;	
	cc_Obj_LatVel[4] = ArcAdas_EmbeddedCoder_Frame_U.ACCObject_5.Lat_Vel;	
	cc_Obj_LatVel[5] = ArcAdas_EmbeddedCoder_Frame_U.ACCObject_6.Lat_Vel;
	
	//OBJ_Abs_Long_Acc
	cc_Obj_LongAccel[0] = ArcAdas_EmbeddedCoder_Frame_U.ACCObject_1.Long_Accel;
	cc_Obj_LongAccel[1] = ArcAdas_EmbeddedCoder_Frame_U.ACCObject_2.Long_Accel;
	cc_Obj_LongAccel[2] = ArcAdas_EmbeddedCoder_Frame_U.ACCObject_3.Long_Accel;
	cc_Obj_LongAccel[3] = ArcAdas_EmbeddedCoder_Frame_U.ACCObject_4.Long_Accel;
	cc_Obj_LongAccel[4] = ArcAdas_EmbeddedCoder_Frame_U.ACCObject_5.Long_Accel;
	cc_Obj_LongAccel[5] = ArcAdas_EmbeddedCoder_Frame_U.ACCObject_6.Long_Accel;	
	
	//OBJ_Long_Distance
    cc_Obj_LongDistance[0] = ArcAdas_EmbeddedCoder_Frame_U.ACCObject_1.Long_Pos - rearAxle2Front_Offset;
    cc_Obj_LongDistance[1] = ArcAdas_EmbeddedCoder_Frame_U.ACCObject_2.Long_Pos - rearAxle2Front_Offset;
    cc_Obj_LongDistance[2] = ArcAdas_EmbeddedCoder_Frame_U.ACCObject_3.Long_Pos - rearAxle2Front_Offset;
    cc_Obj_LongDistance[3] = ArcAdas_EmbeddedCoder_Frame_U.ACCObject_4.Long_Pos - rearAxle2Front_Offset;	
    cc_Obj_LongDistance[4] = ArcAdas_EmbeddedCoder_Frame_U.ACCObject_5.Long_Pos - rearAxle2Front_Offset;	
    cc_Obj_LongDistance[5] = ArcAdas_EmbeddedCoder_Frame_U.ACCObject_6.Long_Pos - rearAxle2Front_Offset;
    
    //OBJ_Abs_Long_Velocity	
    cc_Obj_AbsLongVel[0] =  ArcAdas_EmbeddedCoder_Frame_U.ACCObject_1.Long_Vel;	
    cc_Obj_AbsLongVel[1] =  ArcAdas_EmbeddedCoder_Frame_U.ACCObject_2.Long_Vel;	
    cc_Obj_AbsLongVel[2] =  ArcAdas_EmbeddedCoder_Frame_U.ACCObject_3.Long_Vel;	
    cc_Obj_AbsLongVel[3] =  ArcAdas_EmbeddedCoder_Frame_U.ACCObject_4.Long_Vel;	
    cc_Obj_AbsLongVel[4] =  ArcAdas_EmbeddedCoder_Frame_U.ACCObject_5.Long_Vel;	
    cc_Obj_AbsLongVel[5] =  ArcAdas_EmbeddedCoder_Frame_U.ACCObject_6.Long_Vel;	
   
	//OBJ_Lat_Distance
	cc_Obj_LatDistance[0] = -ArcAdas_EmbeddedCoder_Frame_U.ACCObject_1.Lat_Pos;
	cc_Obj_LatDistance[1] = -ArcAdas_EmbeddedCoder_Frame_U.ACCObject_2.Lat_Pos;	
	cc_Obj_LatDistance[2] = -ArcAdas_EmbeddedCoder_Frame_U.ACCObject_3.Lat_Pos;
	cc_Obj_LatDistance[3] = -ArcAdas_EmbeddedCoder_Frame_U.ACCObject_4.Lat_Pos;	
	cc_Obj_LatDistance[4] = -ArcAdas_EmbeddedCoder_Frame_U.ACCObject_5.Lat_Pos;
	cc_Obj_LatDistance[5] = -ArcAdas_EmbeddedCoder_Frame_U.ACCObject_6.Lat_Pos;	
	
	//OBJ_Object_Class
	cc_Obj_ObjType[0] = ArcAdas_EmbeddedCoder_Frame_U.ACCObject_1.ObjectVehicleType;
	cc_Obj_ObjType[1] = ArcAdas_EmbeddedCoder_Frame_U.ACCObject_2.ObjectVehicleType;
	cc_Obj_ObjType[2] = ArcAdas_EmbeddedCoder_Frame_U.ACCObject_3.ObjectVehicleType;
	cc_Obj_ObjType[3] = ArcAdas_EmbeddedCoder_Frame_U.ACCObject_4.ObjectVehicleType;
	cc_Obj_ObjType[4] = ArcAdas_EmbeddedCoder_Frame_U.ACCObject_5.ObjectVehicleType;	
	cc_Obj_ObjType[5] = ArcAdas_EmbeddedCoder_Frame_U.ACCObject_6.ObjectVehicleType;
	
	//ObjectLocationIndex
	cc_Obj_LocIndex[0] = ArcAdas_EmbeddedCoder_Frame_U.ACCObject_1.ObjectLocationIndex;
	cc_Obj_LocIndex[1] = ArcAdas_EmbeddedCoder_Frame_U.ACCObject_2.ObjectLocationIndex;
	cc_Obj_LocIndex[2] = ArcAdas_EmbeddedCoder_Frame_U.ACCObject_3.ObjectLocationIndex;
	cc_Obj_LocIndex[3] = ArcAdas_EmbeddedCoder_Frame_U.ACCObject_4.ObjectLocationIndex;
	cc_Obj_LocIndex[4] = ArcAdas_EmbeddedCoder_Frame_U.ACCObject_5.ObjectLocationIndex;
	cc_Obj_LocIndex[5] = ArcAdas_EmbeddedCoder_Frame_U.ACCObject_6.ObjectLocationIndex;
}

void update_CIPV_info_Hmi(void)
{
	if(cc_Obj_LocIndex[0] == 1)
	{
        cc_CIPV_ID = cc_Obj_ID[0];
		cc_CIPV_LongDistance = cc_Obj_LongDistance[0];
		cc_CIPV_AbsLongVel = cc_Obj_AbsLongVel[0];
		cc_CIPV_LatDistance = cc_Obj_LatDistance[0];
		cc_CIPV_ObjType = cc_Obj_ObjType[0];
		cc_CIPV_LatVel = cc_Obj_LatVel[0]; 
		cc_CIPV_LongAccel = cc_Obj_LongAccel[0]; 
		cc_CIPV_LocIndex = cc_Obj_LocIndex[0];	
	}
	else if(cc_Obj_LocIndex[1] == 1)
	{
        cc_CIPV_ID = cc_Obj_ID[1];
		cc_CIPV_LongDistance = cc_Obj_LongDistance[1];
		cc_CIPV_AbsLongVel = cc_Obj_AbsLongVel[1];
		cc_CIPV_LatDistance = cc_Obj_LatDistance[1];
		cc_CIPV_ObjType = cc_Obj_ObjType[1];
		cc_CIPV_LatVel = cc_Obj_LatVel[1]; 
		cc_CIPV_LongAccel = cc_Obj_LongAccel[1]; 
		cc_CIPV_LocIndex = cc_Obj_LocIndex[1];			
	}
	else if(cc_Obj_LocIndex[2] == 1)
	{
        cc_CIPV_ID = cc_Obj_ID[2];
		cc_CIPV_LongDistance = cc_Obj_LongDistance[2];
		cc_CIPV_AbsLongVel = cc_Obj_AbsLongVel[2];
		cc_CIPV_LatDistance = cc_Obj_LatDistance[2];
		cc_CIPV_ObjType = cc_Obj_ObjType[2];
		cc_CIPV_LatVel = cc_Obj_LatVel[2]; 
		cc_CIPV_LongAccel = cc_Obj_LongAccel[2]; 
		cc_CIPV_LocIndex = cc_Obj_LocIndex[2];			
	}
	else if(cc_Obj_LocIndex[3] == 1)
	{
        cc_CIPV_ID = cc_Obj_ID[3];
		cc_CIPV_LongDistance = cc_Obj_LongDistance[3];
		cc_CIPV_AbsLongVel = cc_Obj_AbsLongVel[3];
		cc_CIPV_LatDistance = cc_Obj_LatDistance[3];
		cc_CIPV_ObjType = cc_Obj_ObjType[3];
		cc_CIPV_LatVel = cc_Obj_LatVel[3]; 
		cc_CIPV_LongAccel = cc_Obj_LongAccel[3]; 
		cc_CIPV_LocIndex = cc_Obj_LocIndex[3];			
	}
	else if(cc_Obj_LocIndex[4] == 1)
	{
        cc_CIPV_ID = cc_Obj_ID[4];
		cc_CIPV_LongDistance = cc_Obj_LongDistance[4];
		cc_CIPV_AbsLongVel = cc_Obj_AbsLongVel[4];
		cc_CIPV_LatDistance = cc_Obj_LatDistance[4];
		cc_CIPV_ObjType = cc_Obj_ObjType[4];
		cc_CIPV_LatVel = cc_Obj_LatVel[4]; 
		cc_CIPV_LongAccel = cc_Obj_LongAccel[4]; 
		cc_CIPV_LocIndex = cc_Obj_LocIndex[4];			
	}
	else if(cc_Obj_LocIndex[5] == 1)
	{
        cc_CIPV_ID = cc_Obj_ID[5];
		cc_CIPV_LongDistance = cc_Obj_LongDistance[5];
		cc_CIPV_AbsLongVel = cc_Obj_AbsLongVel[5];
		cc_CIPV_LatDistance = cc_Obj_LatDistance[5];
		cc_CIPV_ObjType = cc_Obj_ObjType[5];
		cc_CIPV_LatVel = cc_Obj_LatVel[5]; 
		cc_CIPV_LongAccel = cc_Obj_LongAccel[5]; 
		cc_CIPV_LocIndex = cc_Obj_LocIndex[5];			
	}
    else
	{
        cc_CIPV_ID = 0;
		cc_CIPV_LongDistance = 0;
		cc_CIPV_AbsLongVel = 0;
		cc_CIPV_LatDistance = 0;
		cc_CIPV_ObjType = 0;
		cc_CIPV_LatVel = 0; 
		cc_CIPV_LongAccel = 0; 
		cc_CIPV_LocIndex = 0;			
	}			
}

void update_Lane_info_Hmi(void)
{
	/*left lane*/
	if(ArcAdas_EmbeddedCoder_Frame_U.Arc_LeftLine.Line_Exist_Probability > 0.3)
	{
		if(ArcAdas_EmbeddedCoder_Frame_Y.ADAS_LKAorALCFuncReq > 0)
		{
			cc_LeftLaneColor = 5;
		}
		else
		{
			cc_LeftLaneColor = 1;
		}
        cc_LeftLaneType = 2;
		cc_LeftLane_C0 = -ArcAdas_EmbeddedCoder_Frame_U.Arc_LeftLine.Line_C0;
        cc_LeftLane_C1 = -ArcAdas_EmbeddedCoder_Frame_U.Arc_LeftLine.Line_C1;
        cc_LeftLane_C2 = -ArcAdas_EmbeddedCoder_Frame_U.Arc_LeftLine.Line_C2;
        cc_LeftLane_C3 = -ArcAdas_EmbeddedCoder_Frame_U.Arc_LeftLine.Line_C3;
	}
	else
	{
		cc_LeftLaneColor = 0;
        cc_LeftLaneType = 0;
		cc_LeftLane_C0 = 0;
        cc_LeftLane_C1 = 0;
        cc_LeftLane_C2 = 0;
        cc_LeftLane_C3 = 0;		
	}
	/*right lane*/
	if(ArcAdas_EmbeddedCoder_Frame_U.Arc_RightLine.Line_Exist_Probability > 0.3)
	{
		if(ArcAdas_EmbeddedCoder_Frame_Y.ADAS_LKAorALCFuncReq > 0)
		{
			cc_RightLaneColor = 5;
		}
		else
		{
			cc_RightLaneColor = 1;
		}		
        cc_RightLaneType = 2;
		cc_RightLane_C0 = -ArcAdas_EmbeddedCoder_Frame_U.Arc_RightLine.Line_C0;
        cc_RightLane_C1 = -ArcAdas_EmbeddedCoder_Frame_U.Arc_RightLine.Line_C1;
        cc_RightLane_C2 = -ArcAdas_EmbeddedCoder_Frame_U.Arc_RightLine.Line_C2;
        cc_RightLane_C3 = -ArcAdas_EmbeddedCoder_Frame_U.Arc_RightLine.Line_C3;
	}
	else
	{
		cc_RightLaneColor = 0;
        cc_RightLaneType = 0;
		cc_RightLane_C0 = 0;
        cc_RightLane_C1 = 0;
        cc_RightLane_C2 = 0;
        cc_RightLane_C3 = 0;
	}	
    /*left left lane*/
	if(ArcAdas_EmbeddedCoder_Frame_U.Arc_NextLeftLine.Line_Exist_Probability > 0.3)
	{
		cc_LeftLeftLaneColor = 1;
        cc_LeftLeftLaneType = 2;
		cc_LeftLeftLane_C0 = -ArcAdas_EmbeddedCoder_Frame_U.Arc_NextLeftLine.Line_C0;
        cc_LeftLeftLane_C1 = -ArcAdas_EmbeddedCoder_Frame_U.Arc_NextLeftLine.Line_C1;
        cc_LeftLeftLane_C2 = -ArcAdas_EmbeddedCoder_Frame_U.Arc_NextLeftLine.Line_C2;
        cc_LeftLeftLane_C3 = -ArcAdas_EmbeddedCoder_Frame_U.Arc_NextLeftLine.Line_C3;
	}
    else 
	{
		cc_LeftLeftLaneColor = 0;
        cc_LeftLeftLaneType = 0; 
		cc_LeftLeftLane_C0 = 0;
        cc_LeftLeftLane_C1 = 0;
        cc_LeftLeftLane_C2 = 0;
        cc_LeftLeftLane_C3 = 0;
	}
    /*right right lane*/	
	if(ArcAdas_EmbeddedCoder_Frame_U.Arc_NextRightLine.Line_Exist_Probability > 0.3)
	{
		cc_RightRightLaneColor = 1;
        cc_RightRightLaneType = 2;
        cc_RightRightLane_C0 = -ArcAdas_EmbeddedCoder_Frame_U.Arc_NextRightLine.Line_C0;
        cc_RightRightLane_C1 = -ArcAdas_EmbeddedCoder_Frame_U.Arc_NextRightLine.Line_C1;
        cc_RightRightLane_C2 = -ArcAdas_EmbeddedCoder_Frame_U.Arc_NextRightLine.Line_C2;
        cc_RightRightLane_C3 = -ArcAdas_EmbeddedCoder_Frame_U.Arc_NextRightLine.Line_C3;
	}
	else
	{
		cc_RightRightLaneColor = 0;
        cc_RightRightLaneType = 0;
        cc_RightRightLane_C0 = 0;
        cc_RightRightLane_C1 = 0;
        cc_RightRightLane_C2 = 0;
        cc_RightRightLane_C3 = 0;
	}	
}


uint8_t  cc_ObjTypeTransfer(uint8_t type)
{
	uint8_t ret = 0;
	switch(type)
	{
		case 0:  
		    ret = 2;
		    break;
		case 1:  //car
		    ret = 1;
		    break;
		case 2:  //ped
		    ret = 5;
		    break;
		case 4:  //rider
		    ret = 3;
		    break;
		case 6:  //
		    ret = 1;
		    break;		
		case 7:  //bus
		    ret = 2;
		    break;
		default:
		    ret = 0; // all the car
	}
	return ret;
}

void update_Obj_info_Hmi()
{
	uint8_t i = 0;
	/*obj CIPV*/
	if(cc_CIPV_ID > 0)
	{
        cc_MainObjID = 1;
        cc_Obj1_Type = cc_CIPV_ObjType;
        cc_Obj1_LatDis = cc_CIPV_LatDistance;
        cc_Obj1_LongDis = cc_CIPV_LongDistance;		
	}
	else
	{
		cc_MainObjID = 0;
        cc_Obj1_Type = 0;
        cc_Obj1_LatDis = 0;
	    cc_Obj1_LongDis = 0;		
	}	
	/*obj2*/
	if((cc_Obj_ID[0] > 0)&&(cc_Obj_ID[0] != cc_CIPV_ID))
	{
        cc_Obj2_Type = cc_ObjTypeTransfer(cc_Obj_ObjType[0]);
        cc_Obj2_LatDis = cc_Obj_LatDistance[0];
	    cc_Obj2_LongDis = cc_Obj_LongDistance[0];			
		cc_Obj2_Color = 0;		
	}
	else
	{
        cc_Obj2_Type = 0;
        cc_Obj2_LatDis = 0;
	    cc_Obj2_LongDis = 0;	
		cc_Obj2_Color = 0;			
	}	
	/*obj3*/
	if((cc_Obj_ID[1] > 0)&&(cc_Obj_ID[1] != cc_CIPV_ID))
	{
        cc_Obj3_Type = cc_ObjTypeTransfer(cc_Obj_ObjType[1]);
        cc_Obj3_LatDis = cc_Obj_LatDistance[1];
	    cc_Obj3_LongDis = cc_Obj_LongDistance[1];			
		cc_Obj3_Color = 0;		
	}
	else
	{
        cc_Obj3_Type = 0;
        cc_Obj3_LatDis = 0;
	    cc_Obj3_LongDis = 0;	
		cc_Obj3_Color = 0;			
	}		
	/*obj4*/
	if((cc_Obj_ID[2] > 0)&&(cc_Obj_ID[2] != cc_CIPV_ID))
	{
        cc_Obj4_Type = cc_ObjTypeTransfer(cc_Obj_ObjType[2]);
        cc_Obj4_LatDis = cc_Obj_LatDistance[2];
	    cc_Obj4_LongDis = cc_Obj_LongDistance[2];			
		cc_Obj4_Color = 0;		
	}
	else
	{
        cc_Obj4_Type = 0;
        cc_Obj4_LatDis = 0;
	    cc_Obj4_LongDis = 0;	
		cc_Obj4_Color = 0;			
	}		
	/*obj5*/
	if((cc_Obj_ID[3] > 0)&&(cc_Obj_ID[3] != cc_CIPV_ID))
	{
        cc_Obj5_Type = cc_ObjTypeTransfer(cc_Obj_ObjType[3]);
        cc_Obj5_LatDis = cc_Obj_LatDistance[3];
	    cc_Obj5_LongDis = cc_Obj_LongDistance[3];			
		cc_Obj5_Color = 0;		
	}
	else
	{
        cc_Obj5_Type = 0;
        cc_Obj5_LatDis = 0;
	    cc_Obj5_LongDis = 0;	
		cc_Obj5_Color = 0;			
	}	
	/*obj6*/
	if((cc_Obj_ID[4] > 0)&&(cc_Obj_ID[4] != cc_CIPV_ID))
	{
        cc_Obj6_Type = cc_ObjTypeTransfer(cc_Obj_ObjType[4]);
        cc_Obj6_LatDis = cc_Obj_LatDistance[4];
	    cc_Obj6_LongDis = cc_Obj_LongDistance[4];				
	}
	else
	{
        cc_Obj6_Type = 0;
        cc_Obj6_LatDis = 0;
	    cc_Obj6_LongDis = 0;			
	}		
					
	/*obj7*/
	if((cc_Obj_ID[5] > 0)&&(cc_Obj_ID[5] != cc_CIPV_ID))
	{
        cc_Obj7_Type = cc_ObjTypeTransfer(cc_Obj_ObjType[5]);
        cc_Obj7_LatDis = cc_Obj_LatDistance[5];
	    cc_Obj7_LongDis = cc_Obj_LongDistance[5];				
	}
	else
	{
        cc_Obj7_Type = 0;
        cc_Obj7_LatDis = 0;
	    cc_Obj7_LongDis = 0;			
	}			
					
	/*obj8*/
    cc_Obj8_Type = 0;
    cc_Obj8_LatDis = 0;
	cc_Obj8_LongDis = 0;		
		
	/*obj9*/
    cc_Obj9_Type = 0;
    cc_Obj9_LatDis = 0;
	cc_Obj9_LongDis = 0;		
	
}

void HMI_Task(void)
{
	update_hmi_inputSignal();
	update_CIPV_info_Hmi();
	update_Lane_info_Hmi();
	update_Obj_info_Hmi();
}


































