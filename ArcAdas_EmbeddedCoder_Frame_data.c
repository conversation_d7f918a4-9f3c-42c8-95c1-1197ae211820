/*
 * File: ArcAdas_EmbeddedCoder_Frame_data.c
 *
 * Code generated for Simulink model 'ArcAdas_EmbeddedCoder_Frame'.
 *
 * Model version                  : 7.2225
 * Simulink Coder version         : 9.6 (R2021b) 14-May-2021
 * C/C++ source code generated on : Thu Jul  3 17:31:36 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Custom Processor
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#include "ArcAdas_EmbeddedCoder_Frame.h"
#include "ArcAdas_EmbeddedCoder_Frame_private.h"

/* Invariant block signals (default storage) */
const ConstB_ArcAdas_EmbeddedCoder__T ArcAdas_EmbeddedCoder_Fr_ConstB = {
  2.0,                                 /* '<Root>/Data Type Conversion48' */
  2.0,                                 /* '<S17>/Signal Copy20' */
  0.0,                                 /* '<S946>/Constant' */
  -3.1415,                             /* '<S1190>/Gain' */
  0.0,                                 /* '<Root>/Data Type Conversion11' */
  0.0,                                 /* '<S17>/Signal Copy39' */
  0.0,                                 /* '<Root>/Data Type Conversion10' */
  0.0,                                 /* '<S17>/Signal Copy40' */
  0.0,                                 /* '<Root>/Data Type Conversion53' */
  0.0,                                 /* '<S17>/Signal Copy41' */
  0.0,                                 /* '<Root>/Data Type Conversion52' */
  0.0,                                 /* '<S17>/Signal Copy42' */
  1.0,                                 /* '<S1397>/Switch2' */
  1060.0,                              /* '<S1444>/Add1' */
  1100.0,                              /* '<S1444>/Add' */
  2160.0,                              /* '<S1444>/Add2' */
  0.5092592592592593,                  /* '<S1444>/Divide' */
  1.4946759259259261,                  /* '<S1444>/Product' */
  2.2340561235425245,                  /* '<S1438>/Math Function' */
  2368.0994909550759,                  /* '<S1438>/Product5' */
  0.49074074074074076,                 /* '<S1444>/Divide1' */
  1.4403240740740741,                  /* '<S1444>/Product1' */
  2.0745334383573391,                  /* '<S1438>/Math Function1' */
  2281.9867821930729,                  /* '<S1438>/Product6' */
  4650.0862731481484,                  /* '<S1438>/Add3' */
  0.01,                                /* '<S1438>/Gain2' */
  0.01,                                /* '<S1438>/Gain3' */
  2.2340561235425245,                  /* '<S1438>/Math Function2' */
  2.0745334383573391,                  /* '<S1438>/Math Function3' */

  { 0.0, 1.0, 0.0, 0.0 },              /* '<S1438>/Matrix Concatenate' */

  { 0.0, 0.0, 0.0, 1.0 },              /* '<S1438>/Matrix Concatenate2' */

  { 1.0, 0.0, 0.0, 0.0 },              /* '<S1438>/Matrix Concatenate5' */

  { 0.0, 1.0, 0.0, 0.0 },              /* '<S1438>/Matrix Concatenate6' */

  { 0.0, 0.0, 1.0, 0.0 },              /* '<S1438>/Matrix Concatenate7' */

  { 0.0, 0.0, 0.0, 1.0 },              /* '<S1438>/Matrix Concatenate8' */

  { 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0,
    1.0 },                             /* '<S1438>/Matrix Concatenate9' */
  4650.0862731481484,                  /* '<S1438>/MinMax2' */
  2.2340561235425245,                  /* '<S1439>/Math Function' */
  2368.0994909550759,                  /* '<S1439>/Product5' */
  2.0745334383573391,                  /* '<S1439>/Math Function1' */
  2281.9867821930729,                  /* '<S1439>/Product6' */
  4650.0862731481484,                  /* '<S1439>/Add3' */
  0.7,                                 /* '<S1440>/MinMax' */
  0.30000000000000004,                 /* '<S1440>/Coeff2' */
  3228.5000000000005,                  /* '<S1446>/Product' */
  3111.1000000000004,                  /* '<S1446>/Product2' */
  0.4,                                 /* '<S1462>/MinMax' */
  0.6,                                 /* '<S1462>/Coeff2' */
  25.282750375,                        /* '<S1658>/Math Function' */
  8.6142250000000011,                  /* '<S1658>/Math Function1' */
  8.6142250000000011,                  /* '<S1658>/Math Function2' */
  25.282750375,                        /* '<S1650>/Math Function' */
  8.6142250000000011,                  /* '<S1650>/Math Function1' */
  8.6142250000000011,                  /* '<S1650>/Math Function2' */
  25.282750375,                        /* '<S1647>/Math Function' */
  8.6142250000000011,                  /* '<S1647>/Math Function1' */
  0.9,                                 /* '<S1382>/Gain2' */
  0.9,                                 /* '<S1381>/Gain2' */
  0.9,                                 /* '<S1355>/Gain' */
  0.9,                                 /* '<S1355>/Gain2' */
  0.9,                                 /* '<S1315>/Gain' */
  0.9,                                 /* '<S1318>/Gain' */
  0.5,                                 /* '<S1289>/Gain' */
  0.5,                                 /* '<S1290>/Gain' */
  0.5,                                 /* '<S1277>/Gain' */
  0.5,                                 /* '<S1278>/Gain' */
  0.5,                                 /* '<S1305>/Gain' */
  0.5,                                 /* '<S1306>/Gain' */
  -1.0,                                /* '<S1142>/Gain' */
  0.0,                                 /* '<S1171>/Data Type Conversion' */
  0.65F,                               /* '<S118>/Add1' */
  0.0F,                                /* '<S15>/Data Type Conversion10' */
  0.0F,                                /* '<S15>/Data Type Conversion19' */
  0.0F,                                /* '<S15>/Data Type Conversion20' */
  0.0F,                                /* '<S15>/Data Type Conversion21' */
  0.0F,                                /* '<S15>/Data Type Conversion22' */
  0.0F,                                /* '<S15>/Data Type Conversion23' */
  0.0F,                                /* '<S15>/Data Type Conversion24' */
  0.7F,                                /* '<S720>/Add1' */
  0.7F,                                /* '<S724>/Add1' */
  0.7F,                                /* '<S728>/Add1' */
  0.7F,                                /* '<S732>/Add1' */
  0.7F,                                /* '<S736>/Add1' */
  50.0F,                               /* '<S771>/Product3' */
  -1.0E-6F,                            /* '<S776>/Gain' */
  -1.0F,                               /* '<S774>/Gain' */
  50.0F,                               /* '<S775>/Abs' */
  -1.0F,                               /* '<S775>/Gain' */
  1.0F,                                /* '<S775>/Switch1' */
  50.0F,                               /* '<S775>/Switch' */
  0.1F,                                /* '<S777>/MinMax' */
  0.199999988F,                        /* '<S777>/Prod1' */
  0.6F,                                /* '<S789>/Add1' */
  0.6F,                                /* '<S809>/Add1' */
  15.000001F,                          /* '<S847>/Product' */
  15.000001F,                          /* '<S848>/Product' */
  0.7F,                                /* '<S841>/Add1' */
  -0.125F,                             /* '<S818>/Gain' */
  15.000001F,                          /* '<S854>/Product' */
  75.0F,                               /* '<S855>/Product' */
  0.7F,                                /* '<S822>/Add1' */
  0.0F,                                /* '<S865>/Add' */
  0.099999994F,                        /* '<S910>/Gain' */
  -0.099999994F,                       /* '<S910>/Gain1' */
  0.7F,                                /* '<S916>/Add1' */
  0.3F,                                /* '<S1519>/Add1' */
  0.3F,                                /* '<S1681>/Multiport Switch' */
  0.0075000003F,                       /* '<S1722>/Product' */
  0.0075000003F,                       /* '<S1730>/Product' */
  0.0F,                                /* '<S1390>/Multiport Switch' */
  0.7F,                                /* '<S1894>/Add1' */
  0.7F,                                /* '<S1895>/Add1' */
  0.7F,                                /* '<S1896>/Add1' */
  1.0F,                                /* '<S686>/Max1' */
  0.18F,                               /* '<S686>/Divide1' */
  -0.18F,                              /* '<S686>/Gain' */
  0.18F,                               /* '<S686>/Abs' */
  0.025F,                              /* '<S691>/Switch1' */
  0.025F,                              /* '<S696>/Max' */
  2.0F,                                /* '<S698>/Product1' */
  1.0F,                                /* '<S379>/Max1' */
  0.18F,                               /* '<S379>/Divide1' */
  -0.18F,                              /* '<S379>/Gain' */
  0.18F,                               /* '<S379>/Abs' */
  2.0F,                                /* '<S391>/Product1' */
  300.0F,                              /* '<S341>/Switch7' */
  -128,                                /* '<S1176>/Gain1' */
  0U,                                  /* '<S97>/Data Type Conversion2' */
  0U,                                  /* '<S98>/Data Type Conversion2' */
  0U,                                  /* '<S99>/Data Type Conversion2' */
  0U,                                  /* '<S100>/Data Type Conversion2' */
  0U,                                  /* '<S101>/Data Type Conversion2' */
  0U,                                  /* '<S102>/Data Type Conversion2' */
  0U,                                  /* '<S103>/Data Type Conversion2' */
  0U,                                  /* '<S12>/Constant' */
  2U,                                  /* '<S943>/Signal Copy22' */
  1U,                                  /* '<Root>/Data Type Conversion38' */
  1U,                                  /* '<S17>/Signal Copy29' */
  1U,                                  /* '<S1069>/Compare' */
  1U,                                  /* '<S1071>/Compare' */
  1U,                                  /* '<S1072>/Compare' */
  1U,                                  /* '<S1073>/Compare' */
  0U,                                  /* '<S951>/Constant1' */
  0U,                                  /* '<S1399>/Data Type Conversion' */
  0U,                                  /* '<S1397>/Data Type Conversion5' */
  0U,                                  /* '<S1397>/Data Type Conversion8' */
  0U,                                  /* '<S1390>/Data Type Conversion2' */
  1U,                                  /* '<S1889>/Constant' */
  2U,                                  /* '<S1889>/Constant1' */
  4U,                                  /* '<S1889>/Constant2' */
  3U,                                  /* '<S1889>/Constant3' */
  5U,                                  /* '<S1889>/Constant4' */
  6U,                                  /* '<S1889>/Constant6' */
  0U,                                  /* '<Root>/Constant20' */
  0U,                                  /* '<Root>/Constant4' */
  1,                                   /* '<S1176>/Data Type Conversion' */
  0,                                   /* '<S1176>/Data Type Conversion1' */
  0,                                   /* '<S95>/Constant4' */
  1,                                   /* '<S775>/Relational Operator' */
  1,                                   /* '<S775>/Relational Operator1' */

  { 0, 0, 0 },                         /* '<S941>/Compare' */
  0,                                   /* '<S16>/Logical Operator4' */
  0,                                   /* '<S16>/Logical Operator1' */
  0,                                   /* '<Root>/Data Type Conversion40' */
  0,                                   /* '<S17>/Signal Copy27' */
  0,                                   /* '<Root>/Data Type Conversion47' */
  0,                                   /* '<S17>/Signal Copy21' */
  1,                                   /* '<S943>/Data Type Conversion19' */
  0,                                   /* '<Root>/Data Type Conversion17' */
  0,                                   /* '<S17>/Signal Copy31' */
  0,                                   /* '<Root>/Data Type Conversion42' */
  0,                                   /* '<S17>/Signal Copy26' */
  0,                                   /* '<Root>/Data Type Conversion39' */
  0,                                   /* '<S17>/Signal Copy28' */
  0,                                   /* '<Root>/Data Type Conversion43' */
  0,                                   /* '<S17>/Signal Copy25' */
  0,                                   /* '<Root>/Data Type Conversion45' */
  0,                                   /* '<S17>/Signal Copy23' */
  0,                                   /* '<Root>/Data Type Conversion46' */
  0,                                   /* '<S17>/Signal Copy22' */
  1,                                   /* '<S953>/Logical Operator' */
  1,                                   /* '<S953>/Logical Operator1' */
  1,                                   /* '<S962>/Relational Operator' */
  1,                                   /* '<S961>/Relational Operator1' */
  0,                                   /* '<S967>/Relational Operator1' */
  0,                                   /* '<S967>/Relational Operator2' */
  1,                                   /* '<S965>/Relational Operator12' */
  1,                                   /* '<S964>/Relational Operator7' */
  1,                                   /* '<S955>/Logical Operator' */
  1,                                   /* '<S955>/Logical Operator1' */
  1,                                   /* '<S987>/Relational Operator' */
  1,                                   /* '<S986>/Relational Operator1' */
  0,                                   /* '<S992>/Relational Operator1' */
  0,                                   /* '<S992>/Relational Operator2' */
  1,                                   /* '<S990>/Relational Operator12' */
  1,                                   /* '<S989>/Relational Operator7' */
  1,                                   /* '<S957>/Logical Operator' */
  1,                                   /* '<S957>/Logical Operator1' */
  1,                                   /* '<S1009>/Relational Operator' */
  1,                                   /* '<S1008>/Relational Operator1' */
  0,                                   /* '<S1014>/Relational Operator1' */
  0,                                   /* '<S1014>/Relational Operator2' */
  1,                                   /* '<S1012>/Relational Operator12' */
  1,                                   /* '<S1011>/Relational Operator7' */
  0,                                   /* '<S1038>/Data Type Conversion' */
  1,                                   /* '<S1028>/Data Type Conversion2' */
  0,                                   /* '<S1028>/Data Type Conversion8' */
  0,                                   /* '<S1076>/FCWComp1' */
  1,                                   /* '<S1099>/Compare' */
  1,                                   /* '<S1091>/Compare' */
  1,                                   /* '<S1088>/Compare' */
  1,                                   /* '<S1089>/Compare' */
  1,                                   /* '<S1092>/Compare' */
  1,                                   /* '<S1097>/Compare' */
  1,                                   /* '<S1098>/Compare' */
  1,                                   /* '<S1100>/Compare' */
  0,                                   /* '<S1397>/Data Type Conversion6' */
  1,                                   /* '<S1576>/Compare' */
  1,                                   /* '<S1675>/Logical Operator1' */
  0,                                   /* '<S1079>/FCWComp1' */
  0,                                   /* '<S435>/Constant6' */
  0,                                   /* '<S695>/Compare' */
  1,                                   /* '<S366>/Compare' */

  /* Start of '<S1314>/VehicleObject' */
  {
    0.9                                /* '<S1319>/Gain' */
  }
  ,

  /* End of '<S1314>/VehicleObject' */

  /* Start of '<S1314>/PedestrianObject' */
  {
    0.9                                /* '<S1317>/Gain' */
  }
  ,

  /* End of '<S1314>/PedestrianObject' */

  /* Start of '<S1146>/CyclistPred' */
  {
    -128,                              /* '<S1163>/Gain1' */
    1,                                 /* '<S1163>/Data Type Conversion' */
    0                                  /* '<S1163>/Data Type Conversion1' */
  }
  ,

  /* End of '<S1146>/CyclistPred' */

  /* Start of '<S1146>/PedestrianPred' */
  {
    -128,                              /* '<S1168>/Gain1' */
    1,                                 /* '<S1168>/Data Type Conversion' */
    0                                  /* '<S1168>/Data Type Conversion1' */
  }
  ,

  /* End of '<S1146>/PedestrianPred' */

  /* Start of '<S345>/SlidingM_RightTarget2' */
  {
    1.0F,                              /* '<S645>/Max1' */
    0.18F,                             /* '<S645>/Divide1' */
    -0.18F,                            /* '<S645>/Gain' */
    0.18F,                             /* '<S645>/Abs' */
    0.025F,                            /* '<S650>/Switch1' */
    0.025F,                            /* '<S655>/Max' */
    2.0F,                              /* '<S657>/Product1' */
    0,                                 /* '<S434>/Constant6' */
    0                                  /* '<S654>/Compare' */
  }
  ,

  /* End of '<S345>/SlidingM_RightTarget2' */

  /* Start of '<S345>/SlidingM_RightTarget' */
  {
    1.0F,                              /* '<S604>/Max1' */
    0.18F,                             /* '<S604>/Divide1' */
    -0.18F,                            /* '<S604>/Gain' */
    0.18F,                             /* '<S604>/Abs' */
    0.025F,                            /* '<S609>/Switch1' */
    0.025F,                            /* '<S614>/Max' */
    2.0F,                              /* '<S616>/Product1' */
    0,                                 /* '<S433>/Constant6' */
    0                                  /* '<S613>/Compare' */
  }
  ,

  /* End of '<S345>/SlidingM_RightTarget' */

  /* Start of '<S345>/SlidingM_LeftTarget2' */
  {
    1.0F,                              /* '<S563>/Max1' */
    0.18F,                             /* '<S563>/Divide1' */
    -0.18F,                            /* '<S563>/Gain' */
    0.18F,                             /* '<S563>/Abs' */
    0.025F,                            /* '<S568>/Switch1' */
    0.025F,                            /* '<S573>/Max' */
    2.0F,                              /* '<S575>/Product1' */
    0,                                 /* '<S432>/Constant6' */
    0                                  /* '<S572>/Compare' */
  }
  ,

  /* End of '<S345>/SlidingM_LeftTarget2' */

  /* Start of '<S345>/SlidingM_LeftTarget' */
  {
    1.0F,                              /* '<S522>/Max1' */
    0.18F,                             /* '<S522>/Divide1' */
    -0.18F,                            /* '<S522>/Gain' */
    0.18F,                             /* '<S522>/Abs' */
    0.025F,                            /* '<S527>/Switch1' */
    0.025F,                            /* '<S532>/Max' */
    2.0F,                              /* '<S534>/Product1' */
    0,                                 /* '<S431>/Constant6' */
    0                                  /* '<S531>/Compare' */
  }
  /* End of '<S345>/SlidingM_LeftTarget' */
};

/* Constant parameters (default storage) */
const ConstP_ArcAdas_EmbeddedCoder__T ArcAdas_EmbeddedCoder_Fr_ConstP = {
  /* Pooled Parameter (Expression: [0 5 10 15 20 25 30 35 45 55])
   * Referenced by:
   *   '<S373>/Constant1'
   *   '<S516>/Constant1'
   *   '<S557>/Constant1'
   *   '<S598>/Constant1'
   *   '<S639>/Constant1'
   *   '<S680>/Constant1'
   */
  { 0.0, 5.0, 10.0, 15.0, 20.0, 25.0, 30.0, 35.0, 45.0, 55.0 },

  /* Pooled Parameter (Expression: [0 5 10 15 20 25 30 35 45 55])
   * Referenced by:
   *   '<S383>/1-D Lookup Table3'
   *   '<S383>/1-D Lookup Table4'
   *   '<S383>/1-D Lookup Table5'
   *   '<S384>/1-D Lookup Table7'
   *   '<S385>/1-D Lookup Table'
   *   '<S385>/1-D Lookup Table1'
   *   '<S385>/1-D Lookup Table2'
   *   '<S387>/1-D Lookup Table2'
   *   '<S387>/1-D Lookup Table3'
   *   '<S526>/1-D Lookup Table3'
   *   '<S526>/1-D Lookup Table4'
   *   '<S526>/1-D Lookup Table5'
   *   '<S527>/1-D Lookup Table7'
   *   '<S528>/1-D Lookup Table'
   *   '<S528>/1-D Lookup Table1'
   *   '<S528>/1-D Lookup Table2'
   *   '<S530>/1-D Lookup Table2'
   *   '<S530>/1-D Lookup Table3'
   *   '<S567>/1-D Lookup Table3'
   *   '<S567>/1-D Lookup Table4'
   *   '<S567>/1-D Lookup Table5'
   *   '<S568>/1-D Lookup Table7'
   *   '<S569>/1-D Lookup Table'
   *   '<S569>/1-D Lookup Table1'
   *   '<S569>/1-D Lookup Table2'
   *   '<S571>/1-D Lookup Table2'
   *   '<S571>/1-D Lookup Table3'
   *   '<S608>/1-D Lookup Table3'
   *   '<S608>/1-D Lookup Table4'
   *   '<S608>/1-D Lookup Table5'
   *   '<S609>/1-D Lookup Table7'
   *   '<S610>/1-D Lookup Table'
   *   '<S610>/1-D Lookup Table1'
   *   '<S610>/1-D Lookup Table2'
   *   '<S612>/1-D Lookup Table2'
   *   '<S612>/1-D Lookup Table3'
   *   '<S649>/1-D Lookup Table3'
   *   '<S649>/1-D Lookup Table4'
   *   '<S649>/1-D Lookup Table5'
   *   '<S650>/1-D Lookup Table7'
   *   '<S651>/1-D Lookup Table'
   *   '<S651>/1-D Lookup Table1'
   *   '<S651>/1-D Lookup Table2'
   *   '<S653>/1-D Lookup Table2'
   *   '<S653>/1-D Lookup Table3'
   *   '<S690>/1-D Lookup Table3'
   *   '<S690>/1-D Lookup Table4'
   *   '<S690>/1-D Lookup Table5'
   *   '<S691>/1-D Lookup Table7'
   *   '<S692>/1-D Lookup Table'
   *   '<S692>/1-D Lookup Table1'
   *   '<S692>/1-D Lookup Table2'
   *   '<S694>/1-D Lookup Table2'
   *   '<S694>/1-D Lookup Table3'
   */
  { 0.0F, 5.0F, 10.0F, 15.0F, 20.0F, 25.0F, 30.0F, 35.0F, 45.0F, 55.0F },

  /* Pooled Parameter (Expression: [3 3 3 5 6 7 8 9 9 10])
   * Referenced by:
   *   '<S384>/1-D Lookup Table4'
   *   '<S527>/1-D Lookup Table4'
   *   '<S568>/1-D Lookup Table4'
   *   '<S609>/1-D Lookup Table4'
   *   '<S650>/1-D Lookup Table4'
   *   '<S691>/1-D Lookup Table4'
   */
  { 3.0F, 3.0F, 3.0F, 5.0F, 6.0F, 7.0F, 8.0F, 9.0F, 9.0F, 10.0F },

  /* Pooled Parameter (Expression: [1 0.2 0.01 0.01 0.01 0.01 0.01 0.01 0.01 0.01])
   * Referenced by:
   *   '<S383>/1-D Lookup Table5'
   *   '<S526>/1-D Lookup Table5'
   *   '<S567>/1-D Lookup Table5'
   *   '<S608>/1-D Lookup Table5'
   *   '<S649>/1-D Lookup Table5'
   *   '<S690>/1-D Lookup Table5'
   */
  { 1.0F, 0.2F, 0.01F, 0.01F, 0.01F, 0.01F, 0.01F, 0.01F, 0.01F, 0.01F },

  /* Pooled Parameter (Expression: [1 1.5 1.5 1.5 1.5 1.5 1.5 1.5 1.5 1.5])
   * Referenced by:
   *   '<S384>/1-D Lookup Table5'
   *   '<S527>/1-D Lookup Table5'
   *   '<S568>/1-D Lookup Table5'
   *   '<S609>/1-D Lookup Table5'
   *   '<S650>/1-D Lookup Table5'
   *   '<S691>/1-D Lookup Table5'
   */
  { 1.0F, 1.5F, 1.5F, 1.5F, 1.5F, 1.5F, 1.5F, 1.5F, 1.5F, 1.5F },

  /* Pooled Parameter (Expression: [0.5 1])
   * Referenced by:
   *   '<S384>/1-D Lookup Table6'
   *   '<S527>/1-D Lookup Table6'
   *   '<S568>/1-D Lookup Table6'
   *   '<S609>/1-D Lookup Table6'
   *   '<S650>/1-D Lookup Table6'
   *   '<S691>/1-D Lookup Table6'
   */
  { 0.5F, 1.0F },

  /* Pooled Parameter (Expression: [1 3])
   * Referenced by:
   *   '<S384>/1-D Lookup Table6'
   *   '<S527>/1-D Lookup Table6'
   *   '<S568>/1-D Lookup Table6'
   *   '<S609>/1-D Lookup Table6'
   *   '<S650>/1-D Lookup Table6'
   *   '<S691>/1-D Lookup Table6'
   */
  { 1.0F, 3.0F },

  /* Computed Parameter: k_ACC_TakeOffTimeGapIndexSpdDis
   * Referenced by: '<S749>/k_ACC_TakeOffTimeGapIndexSpdDisable'
   */
  { 1.5F, 0.0F, 0.0F, 0.0F, 0.0F },

  /* Computed Parameter: k_ACC_PosTargetFfwd_TiGapGain_V
   * Referenced by: '<S752>/k_ACC_PosTargetFfwd_TiGapGain'
   */
  { 0.33F, 0.29F, 0.24F, 0.22F, 0.19F },

  /* Computed Parameter: uDLookupTable1_bp01Data
   * Referenced by: '<S114>/1-D Lookup Table1'
   */
  { 0.0F, 10.0F, 20.0F, 30.0F, 40.0F },

  /* Pooled Parameter (Expression: [0.25 0.2 0])
   * Referenced by:
   *   '<S132>/1-D Lookup Table1'
   *   '<S132>/1-D Lookup Table2'
   */
  { 0.25F, 0.2F, 0.0F },

  /* Pooled Parameter (Expression: [10 20 40])
   * Referenced by:
   *   '<S132>/1-D Lookup Table1'
   *   '<S132>/1-D Lookup Table2'
   */
  { 10.0F, 20.0F, 40.0F },

  /* Pooled Parameter (Expression: [0.35, 0.4, 0.45, 0.5, 0.55, 0.6])
   * Referenced by:
   *   '<S96>/1-D Lookup Table2'
   *   '<S114>/1-D Lookup Table2'
   */
  { 0.35F, 0.4F, 0.45F, 0.5F, 0.55F, 0.6F },

  /* Pooled Parameter (Expression: [1/1000, 1/800, 1/600, 1/400, 1/200, 1/50])
   * Referenced by:
   *   '<S96>/1-D Lookup Table2'
   *   '<S114>/1-D Lookup Table2'
   */
  { 0.001F, 0.00125F, 0.00166666671F, 0.0025F, 0.005F, 0.02F },

  /* Computed Parameter: uDLookupTable_bp01Data
   * Referenced by: '<S215>/1-D Lookup Table'
   */
  { 0.0F, 20.0F, 40.0F, 60.0F, 80.0F },

  /* Computed Parameter: uDLookupTable_tableData
   * Referenced by: '<S867>/2-D Lookup Table'
   */
  { 50.0F, 55.0F, 65.0F, 100.0F, 130.0F, 40.0F, 50.0F, 60.0F, 90.0F, 130.0F,
    40.0F, 40.0F, 55.0F, 80.0F, 120.0F, 30.0F, 40.0F, 50.0F, 70.0F, 110.0F,
    30.0F, 35.0F, 50.0F, 70.0F, 100.0F },

  /* Computed Parameter: uDLookupTable_bp01Data_e
   * Referenced by: '<S867>/2-D Lookup Table'
   */
  { 0.0F, 20.0F, 30.0F, 40.0F, 50.0F },

  /* Computed Parameter: uDLookupTable_bp02Data
   * Referenced by: '<S867>/2-D Lookup Table'
   */
  { 0.0F, 0.05F, 0.1F, 0.15F, 0.2F },

  /* Pooled Parameter (Expression: [80 120 150])
   * Referenced by:
   *   '<S334>/1-D Lookup Table'
   *   '<S335>/1-D Lookup Table'
   *   '<S336>/1-D Lookup Table'
   *   '<S337>/1-D Lookup Table'
   *   '<S338>/1-D Lookup Table'
   *   '<S339>/1-D Lookup Table'
   *   '<S340>/1-D Lookup Table'
   *   '<S353>/1-D Lookup Table'
   *   '<S446>/1-D Lookup Table'
   *   '<S447>/1-D Lookup Table'
   *   '<S448>/1-D Lookup Table'
   *   '<S449>/1-D Lookup Table'
   *   '<S450>/1-D Lookup Table'
   */
  { 80.0F, 120.0F, 150.0F },

  /* Pooled Parameter (Expression: [0 20 40])
   * Referenced by:
   *   '<S334>/1-D Lookup Table'
   *   '<S335>/1-D Lookup Table'
   *   '<S336>/1-D Lookup Table'
   *   '<S337>/1-D Lookup Table'
   *   '<S338>/1-D Lookup Table'
   *   '<S339>/1-D Lookup Table'
   *   '<S340>/1-D Lookup Table'
   *   '<S353>/1-D Lookup Table'
   *   '<S446>/1-D Lookup Table'
   *   '<S447>/1-D Lookup Table'
   *   '<S448>/1-D Lookup Table'
   *   '<S449>/1-D Lookup Table'
   *   '<S450>/1-D Lookup Table'
   */
  { 0.0F, 20.0F, 40.0F },

  /* Computed Parameter: k_TargeHostAccelDiffSpdMap_tabl
   * Referenced by: '<S748>/k_TargeHostAccelDiffSpdMap'
   */
  { -3.0F, -2.7F, -1.9F, -1.5F, -1.5F },

  /* Computed Parameter: k_TargeHostAccelDiffSpdMap_bp01
   * Referenced by: '<S748>/k_TargeHostAccelDiffSpdMap'
   */
  { 0.0F, 10.0F, 15.0F, 17.0F, 56.0F },

  /* Computed Parameter: k_TargeHostAccelDiff_TG_tableDa
   * Referenced by: '<S748>/k_TargeHostAccelDiff_TG'
   */
  { 1.0F, 1.0F, 1.0F, 2.0F },

  /* Computed Parameter: k_TargeHostAccelDiff_TG_bp01Dat
   * Referenced by: '<S748>/k_TargeHostAccelDiff_TG'
   */
  { 0.0F, 1.0F, 2.0F, 4.0F },

  /* Computed Parameter: k_TG_TarAccelFilterConst_tableD
   * Referenced by: '<S750>/k_TG_TarAccelFilterConst'
   */
  { 0.8F, 0.07F, 0.05F, 0.045F, 0.04F, 0.035F, 0.03F },

  /* Computed Parameter: k_TG_TarAccelFilterConst_bp01Da
   * Referenced by: '<S750>/k_TG_TarAccelFilterConst'
   */
  { 0.0F, 0.5F, 0.8F, 1.0F, 1.5F, 2.0F, 3.0F },

  /* Computed Parameter: k_NegTarAccelGain_tableData
   * Referenced by: '<S752>/k_NegTarAccelGain'
   */
  { 1.6F, 0.93F, 0.0F },

  /* Computed Parameter: k_NegTarAccelGain_bp01Data
   * Referenced by: '<S752>/k_NegTarAccelGain'
   */
  { 0.0F, 0.8F, 4.0F },

  /* Computed Parameter: NegFfwdSpeedGainMap_tableData
   * Referenced by: '<S752>/NegFfwdSpeedGainMap'
   */
  { 1.18F, 1.1747F, 1.1685F, 1.1577F, 1.1358F, 1.038F, 1.0184F, 1.008F, 1.0F },

  /* Computed Parameter: NegFfwdSpeedGainMap_bp01Data
   * Referenced by: '<S752>/NegFfwdSpeedGainMap'
   */
  { 0.0F, 11.0F, 13.0F, 15.0F, 17.0F, 23.0F, 25.0F, 27.0F, 29.0F },

  /* Computed Parameter: k_NegTarTTC_Saturation_tableDat
   * Referenced by: '<S772>/k_NegTarTTC_Saturation'
   */
  { -3.0F, 0.0F },

  /* Computed Parameter: k_NegTarTTC_Saturation_bp01Data
   * Referenced by: '<S772>/k_NegTarTTC_Saturation'
   */
  { 0.0F, 10.0F },

  /* Pooled Parameter (Expression: [-4 -4 -4 -4 -4 -4 -4])
   * Referenced by:
   *   '<S436>/lookup2'
   *   '<S437>/lookup2'
   *   '<S438>/lookup2'
   *   '<S439>/lookup2'
   *   '<S440>/lookup2'
   */
  { -4.0F, -4.0F, -4.0F, -4.0F, -4.0F, -4.0F, -4.0F },

  /* Pooled Parameter (Expression: [0 7 10 15 22 28 33])
   * Referenced by:
   *   '<S269>/lookup2'
   *   '<S436>/lookup2'
   *   '<S437>/lookup2'
   *   '<S438>/lookup2'
   *   '<S439>/lookup2'
   *   '<S440>/lookup2'
   */
  { 0.0F, 7.0F, 10.0F, 15.0F, 22.0F, 28.0F, 33.0F },

  /* Computed Parameter: lookup2_tableData
   * Referenced by: '<S269>/lookup2'
   */
  { -4.5F, -4.5F, -4.0F, -4.0F, -4.0F, -3.5F, -3.5F },

  /* Computed Parameter: uDLookupTable_tableData_d
   * Referenced by: '<S354>/2-D Lookup Table'
   */
  { 2.0F, 3.5F, 3.5F, 4.0F, 4.5F, 4.5F, 5.0F, 8.0F, 8.0F, 9.0F, 10.0F, 10.5F,
    12.0F, 14.0F, 12.0F, 13.8F, 15.0F, 17.0F, 20.0F, 25.0F, 30.0F, 14.0F, 18.8F,
    22.0F, 30.0F, 40.0F, 56.0F, 72.0F, 25.0F, 40.0F, 50.0F, 75.0F, 100.0F,
    120.0F, 140.0F, 30.0F, 48.0F, 60.0F, 90.0F, 120.0F, 140.0F, 140.0F },

  /* Computed Parameter: uDLookupTable_bp01Data_a
   * Referenced by: '<S354>/2-D Lookup Table'
   */
  { 0.5F, 0.8F, 1.0F, 1.4F, 1.8F, 2.2F, 2.6F },

  /* Computed Parameter: uDLookupTable_bp02Data_f
   * Referenced by: '<S354>/2-D Lookup Table'
   */
  { 0.0F, 2.5F, 10.0F, 20.0F, 50.0F, 60.0F },

  /* Computed Parameter: uDLookupTable_bp02Data_l
   * Referenced by: '<S1745>/1-D Lookup Table'
   */
  { 0.0001F, 0.0005F, 0.00067F, 0.001F, 0.002F, 0.004F, 0.0067F, 0.01F, 0.02F },

  /* Computed Parameter: uDLookupTable_maxIndex
   * Referenced by: '<S341>/2-D Lookup Table'
   */
  { 8U, 8U },

  /* Pooled Parameter (Expression: )
   * Referenced by:
   *   '<S384>/TP_A_IDX1_LB_cu_z'
   *   '<S384>/TP_V_IDX1_catchup_cu_z'
   *   '<S527>/TP_A_IDX1_LB_cu_z'
   *   '<S527>/TP_V_IDX1_catchup_cu_z'
   *   '<S568>/TP_A_IDX1_LB_cu_z'
   *   '<S568>/TP_V_IDX1_catchup_cu_z'
   *   '<S609>/TP_A_IDX1_LB_cu_z'
   *   '<S609>/TP_V_IDX1_catchup_cu_z'
   *   '<S650>/TP_A_IDX1_LB_cu_z'
   *   '<S650>/TP_V_IDX1_catchup_cu_z'
   *   '<S691>/TP_A_IDX1_LB_cu_z'
   *   '<S691>/TP_V_IDX1_catchup_cu_z'
   */
  { 4U, 9U },

  /* Computed Parameter: VehicleLookUpTable_maxIndex
   * Referenced by: '<S1311>/Vehicle Look-Up Table'
   */
  { 6U, 12U },

  /* Computed Parameter: VehicleLookUpTable_maxIndex_j
   * Referenced by: '<S1307>/Vehicle Look-Up Table'
   */
  { 6U, 13U },

  /* Pooled Parameter (Expression: )
   * Referenced by:
   *   '<S1271>/Vehicle Look-Up Table'
   *   '<S1275>/Vehicle Look-Up Table'
   */
  { 4U, 13U },

  /* Pooled Parameter (Expression: )
   * Referenced by:
   *   '<S1272>/Vehicle Look-Up Table'
   *   '<S1274>/Vehicle Look-Up Table'
   *   '<S1281>/Look-Up Table'
   *   '<S1283>/Look-Up Table'
   *   '<S1295>/Vehicle Look-Up Table'
   *   '<S1300>/Vehicle Look-Up Table'
   */
  { 3U, 14U },

  /* Pooled Parameter (Expression: )
   * Referenced by:
   *   '<S1280>/Look-Up Table'
   *   '<S1284>/Look-Up Table'
   *   '<S1285>/Look-Up Table'
   *   '<S1286>/Look-Up Table'
   *   '<S1287>/Look-Up Table'
   */
  { 4U, 14U },

  /* Computed Parameter: uDLookupTable_maxIndex_l
   * Referenced by: '<S867>/2-D Lookup Table'
   */
  { 4U, 4U },

  /* Pooled Parameter (Expression: )
   * Referenced by:
   *   '<S863>/2-D Lookup Table'
   *   '<S1464>/1-D Lookup Table'
   */
  { 6U, 6U },

  /* Computed Parameter: uDLookupTable_maxIndex_d
   * Referenced by: '<S354>/2-D Lookup Table'
   */
  { 6U, 5U },

  /* Computed Parameter: uDLookupTable_maxIndex_h
   * Referenced by: '<S1745>/1-D Lookup Table'
   */
  { 3U, 8U },

  /* Pooled Parameter (Expression: )
   * Referenced by:
   *   '<S1428>/2-D Lookup Table'
   *   '<S1445>/2-D Lookup Table'
   */
  { 14U, 19U },

  /* Pooled Parameter (Expression: )
   * Referenced by:
   *   '<S1458>/2-D Lookup Table1'
   *   '<S1458>/2-D Lookup Table10'
   *   '<S1458>/2-D Lookup Table11'
   *   '<S1458>/2-D Lookup Table12'
   *   '<S1458>/2-D Lookup Table13'
   *   '<S1458>/2-D Lookup Table14'
   *   '<S1458>/2-D Lookup Table15'
   *   '<S1458>/2-D Lookup Table2'
   *   '<S1458>/2-D Lookup Table3'
   *   '<S1458>/2-D Lookup Table4'
   *   '<S1458>/2-D Lookup Table5'
   *   '<S1458>/2-D Lookup Table6'
   *   '<S1458>/2-D Lookup Table7'
   *   '<S1458>/2-D Lookup Table8'
   *   '<S1458>/2-D Lookup Table9'
   */
  { 14U, 30U },

  /* Computed Parameter: uDLookupTable_maxIndex_p
   * Referenced by: '<S1458>/1-D Lookup Table'
   */
  { 6U, 35U },

  /* Pooled Parameter (Expression: [0 1;1 0;0 1;0 1;1 0;1 0;0 0;0 0])
   * Referenced by:
   *   '<S257>/Logic'
   *   '<S929>/Logic'
   *   '<S935>/Logic'
   *   '<S938>/Logic'
   *   '<S76>/Logic'
   *   '<S982>/Logic'
   *   '<S1005>/Logic'
   *   '<S1027>/Logic'
   *   '<S137>/Logic'
   *   '<S840>/Logic'
   *   '<S849>/Logic'
   *   '<S856>/Logic'
   *   '<S880>/Logic'
   *   '<S892>/Logic'
   *   '<S1056>/Logic'
   *   '<S1080>/Logic'
   *   '<S1841>/Logic'
   *   '<S283>/Logic'
   *   '<S1800>/Logic'
   *   '<S306>/Logic'
   *   '<S1141>/Logic'
   *   '<S1711>/Logic'
   *   '<S1712>/Logic'
   *   '<S753>/Logic'
   *   '<S1162>/Logic'
   *   '<S1167>/Logic'
   *   '<S1175>/Logic'
   *   '<S783>/Logic'
   */
  { false, true, false, false, true, true, false, false, true, false, true, true,
    false, false, false, false },

  /* Computed Parameter: Constant_Value_i3
   * Referenced by: '<S51>/Constant'
   */
  { 1U, 2U, 3U, 4U, 5U },

  /* Computed Parameter: Constant_Value_d
   * Referenced by: '<S1786>/Constant'
   */
  { 2U, 3U, 4U, 5U, 6U, 7U }
};

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
