{"version": "0.2.0", "configurations": [{"name": "C/C++ Runner: Debug Session", "type": "cppdbg", "request": "launch", "args": [], "stopAtEntry": false, "externalConsole": true, "cwd": "//hz-iotfs/控制算法/66_ShareFolder/Wangshaohui/04_SouthLake_LiOne/ArcAdas_EmbeddedCoder_Frame_ert_rtw", "program": "//hz-iotfs/控制算法/66_ShareFolder/Wangshaohui/04_SouthLake_LiOne/ArcAdas_EmbeddedCoder_Frame_ert_rtw/build/Debug/outDebug", "MIMode": "gdb", "miDebuggerPath": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}]}]}