#include "arcsoft_adas_connection_SL.h"
// for auto build system
#define VERSION_CODEBASE 0
#define VERSION_MAJOR   3
#define VERSION_MINOR   0
#define VERSION_BUILD   5
#define VERSION_DATE    "03/07/2025"
#define VERSION_VERSION "ArcSoft_ArcAdas_0.3.0.5"
#define VERSION_COPYRIGHT "Copyright 2022 ArcSoft, Inc. All rights reserved."

const ArcAdas_Version *ArcAdas_GetVersion() {
    static ArcAdas_Version s_ver = {
            VERSION_CODEBASE,
            VERSION_MAJOR,
            VERSION_MINOR,
            VERSION_BUILD,
            (char*)(VERSION_VERSION " (" __DATE__ " " __TIME__ ")"),
            (char*)(VERSION_DATE),
            (char*)(VERSION_COPYRIGHT)
    };
    return &s_ver;
}
