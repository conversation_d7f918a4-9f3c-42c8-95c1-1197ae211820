////////////////////////////////////////////////////////////////////////////////////
/// Copyright(c) ArcSoft, All right reserved.
///
/// This file is ArcSoft's property. It contains ArcSoft's trade secret, proprietary
/// and confidential information.
///
/// The information and code contained in this file is only for authorized ArcSoft
/// employees to design, create, modify, or review.
///
/// DO NOT DISTRIBUTE, DO NOT DUPLICATE OR TRANSMIT IN ANY FORM WITHOUT PROPER
/// AUTHORIZATION.
///
/// If you are not an intended recipient of this file, you must not copy,
/// distribute, modify, or take any action in reliance on it.
///
/// If you have received this file in error, please immediately notify ArcSoft and
/// permanently delete the original and any copy of any file and any printout
/// thereof.
///
/// @file       arcsoft_adas_connection.h
///
/// @brief      ArcSoft adas engine.
///
/// @version    *******
///
/// @date       12/12/2024
///
/// @note
///
////////////////////////////////////////////////////////////////////////////////

#ifndef _ARCSOFT_ADAS_LKA_H_
#define _ARCSOFT_ADAS_LKA_H_

#   if defined(_WINDOWS) || defined(__CYGWIN__)
#       define ARC_DLL __declspec(dllexport)
#   elif defined(__GNUC__) && __GNUC__ >= 4
#       define ARC_DLL __attribute__ ((visibility ("default")))
#   else
#       define ARC_DLL
#   endif

#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

#define    LINE_MAX_NUM          6  // 0,1,2,3
// #define    RE_MAX_NUM         2
#define    Obj_MAX_NUM           64 // 0,1,2,...18,19

#define    HOST_LEFT_LINE        0
#define    HOST_RIGHT_LINE       1
#define    NEXT_LEFT_LINE        2
#define    NEXT_RIGHT_LINE       3

#define    LEFT_RE               4
#define    RIGHT_RE              5

#define    Object_No_0           0
#define    Object_No_1           1
#define    Object_No_2           2
#define    Object_No_3           3
#define    Object_No_4           4
#define    Object_No_5           5
#define    Object_No_6           6
#define    Object_No_7           7
#define    Object_No_8           8
#define    Object_No_9           9
#define    Object_No_10          10
#define    Object_No_11          11
#define    Object_No_12          12
#define    Object_No_13          13
#define    Object_No_14          14
#define    Object_No_15          15
#define    Object_No_16          16
#define    Object_No_17          17
#define    Object_No_18          18
#define    Object_No_19          19
#define    Object_No_20          20
#define    Object_No_21          21
#define    Object_No_22          22
#define    Object_No_23          23
#define    Object_No_24          24
#define    Object_No_25          25
#define    Object_No_26          26
#define    Object_No_27          27
#define    Object_No_28          28
#define    Object_No_29          29
#define    Object_No_30          30
#define    Object_No_31          31
#define    Object_No_32          32
#define    Object_No_33          33
#define    Object_No_34          34
#define    Object_No_35          35
#define    Object_No_36          36
#define    Object_No_37          37
#define    Object_No_38          38
#define    Object_No_39          39
#define    Object_No_40          40
#define    Object_No_41          41
#define    Object_No_42          42
#define    Object_No_43          43
#define    Object_No_44          44
#define    Object_No_45          45
#define    Object_No_46          46
#define    Object_No_47          47
#define    Object_No_48          48
#define    Object_No_49          49
#define    Object_No_50          50
#define    Object_No_51          51
#define    Object_No_52          52
#define    Object_No_53          53
#define    Object_No_54          54
#define    Object_No_55          55
#define    Object_No_56          56
#define    Object_No_57          57
#define    Object_No_58          58
#define    Object_No_59          59
#define    Object_No_60          60
#define    Object_No_61          61
#define    Object_No_62          62
#define    Object_No_63          63

#define    DEBUG_NUM             100
#define    ROADMARK_ELEM_BUS_MAX 8
#define    FSPX_ELEM_BUS_MAX     20
#define    TarSelnPathNODE       100
#define    FCF_MAX_NUM_VEH 8          //支持的配置最大veh个数
#define    FCF_MAX_NUM_VRU 8          //支持的配置最大vru个数
#define    ACC_TARGET_NUM 6           // 感知端传来的ACC目标最大个数

/********识别的车道线参数结构体********/

	typedef struct _arcLineJunctionPoint_ {
		int32_t s32PointId;
		int32_t s32PointAge;
		int32_t s32SplitMergeType;                                         // The class of split or merge point, unknown (0), split point(1), merge point (2)
		float   f32ImagePointX;
		float   f32ImagePointY;
		float   f32WorldPointX;
		float   f32WorldPointY;
		float   f32Confidence;
	} ArcLineJunctionPointList;

    // ADAS platform input	
    typedef struct {
        uint8_t  ArcCam_Line_TrackID;
        uint8_t  ArcCam_Line_Age;                                          //车道线被识别的帧数
        float    ArcCam_Line_ExistProbability;
        uint8_t  ArcCam_Line_Crossing;
        uint8_t  ArcCam_Line_TypeClass;
        float    ArcCam_Line_RangeStart;
        float    ArcCam_Line_RangeEnd;
        uint8_t  ArcCam_Line_Color;
        float    ArcCam_Line_LaneMarkerWidth;
        float    ArcCam_Line_DashAverageLength;
        float    ArcCam_Line_DashAverageGap;
        uint8_t  ArcCam_Line_PredictionSource;
        float    ArcCam_Line_C0;
        float    ArcCam_Line_C1;
        float    ArcCam_Line_C2;
        float    ArcCam_Line_C3;
		ArcLineJunctionPointList LanePointListMessage;
        uint64_t CameraExposureSyncTimestamp;                              //感知摄像头曝光时间戳，用于计算延迟补偿
    } ARCAdas_ArcCamLaneData_STRUCTURE; // line in info
/*
    typedef struct {
		uint8_t  ArcCam_RE01_LineID;
		uint8_t  ArcCam_RE01_Type;
		uint8_t  ArcCam_RE01_MeasureType;
        float    ArcCam_RE01_ExistProb;
        float    ArcCam_RE01_C0;
        float    ArcCam_RE01_C1;
        float    ArcCam_RE01_C2;
        float    ArcCam_RE01_C3;
        float    ArcCam_RE01_DxEnd;
        float    ArcCam_RE01_DxStart;
    } ARCAdas_ArcCamRdEdgeData_STRUCTURE; // road edge info
*/

	typedef struct {
        uint64_t FC_System_Time_Stamp;                                       //当前的系统时间戳，用于计算延迟时间
		uint64_t FC_LD_DelayTime;                                            //车道线滞后时间，系统计算完成之后，直接传入
		uint64_t FC_OBJ_DelayTime;                                           //目标物滞后时间，系统计算完成之后，直接传入
		uint64_t FrameIdIn;                                               // 当前图像输入帧Id
    }ARCAdas_ArcCamHeader_STRUCTURE;                                      // camera header info  
	
	/********识别的斑马线结构体********/
	typedef struct __arcRoadMarkElemBus__ {
		uint8_t u8Class;                                                  // 1:斑马线；2:停止线
		float f32LineDistance;
	} ArcRoadMarkElemBus;

	typedef struct __arcBaseHdrBus__ {
		uint8_t u8Count;                                                 //识别结果的个数
		uint8_t u8SyncId;                                                //同步 ID 用于在系统中的信号之间进行同步
		ARCAdas_ArcCamHeader_STRUCTURE time;                             //时间戳
	} ArcBaseHdrBus;
	
	typedef struct __arcRoadMarkBus__ {
		ArcBaseHdrBus hdr;
		ArcRoadMarkElemBus elem[ROADMARK_ELEM_BUS_MAX];
	} ArcRoadMarkBus;

	/********识别的可行驶区域结构体********/
	typedef struct __arcFspxElemBus__ {
		float f32Confidence;                                            //置信度
		uint8_t u8CameraSource;
	} ArcFspxElemBus;

	typedef struct __arcFspxBus__ {
		ArcBaseHdrBus hdr;
		ArcFspxElemBus elem[FSPX_ELEM_BUS_MAX];
	} ArcFspxBus;

	typedef struct __arcFsElemBus__ {
		uint8_t u8FullBlockage;
		uint8_t u8PartialBlockage;
		uint8_t u8ImageWarning;
		uint8_t u8ImageCover;
		uint8_t u8ImageDirty;
		uint8_t u8CalibError;
		uint8_t u8CalibParamerror;
		uint8_t u8CalibOutRange;
	} ArcFsElemBus;

	typedef struct __arcFsBus__ {
		ArcFsElemBus elem[4];
	} ArcFsBus;

    typedef struct {
        uint64_t OBJ_ID;
        uint8_t  OBJ_Object_Class;                                    //目标类型0=truck，1=car，2=pedestrain,4=rider(bike、motorbike、tricycle) 5=traffic zone
								                                     //6=other car  7=bus 8=unknown 11=traffic sign
        float   OBJ_Existence_Probability;
        uint8_t OBJ_Measuring_Status;                                //测量状态。0=FirstFrame 1=Predicted 2=Measured 3=unknown
        uint8_t Motion_Status;                                       //目标状态：0=Moving 1=Stationary 2=Stopped 3=unknown
        uint8_t Motion_Category;                                     //目标运动类别：0-invalid 1-undefined 2-passing 3-passing_in 4-passing_out 5-close_cut_in 
									                                 //  6-moving_in 7-moving_out 8-crossing 9-LTAP 10-RTAP 11-moving 12-preceeding 13-oncoming
        uint8_t Object_Age;
        float   OBJ_Length;
        float   OBJ_Width;
        float   OBJ_Abs_Long_Velocity;
        float   OBJ_Abs_Lat_Velocity;
        float   OBJ_Abs_Long_Acc;
        float   OBJ_Heading_Angle;
        float   OBJ_Long_Distance;
        float   OBJ_Abs_Lat_Acc;
        float   OBJ_Lat_Distance;
        float   OBJ_Angle_Rate;
        float   OBJ_Inv_TTC;
		float   OBJ_WidthStd;                                       //宽度偏差
		float   OBJ_LengthStd;                                      //长度偏差
		float   OBJ_Height;                                         //世界坐标系中目标高度
		float   OBJ_HeightStd;                                      //高度偏差
		float   OBJ_LongDistanceStd;                                //纵向距离偏差
		float   OBJ_LatDistanceStd;                                 //横向距离偏差
		float   OBJ_AbsLongVelocityStd;                             //绝对地速偏差
		float   OBJ_LongRelativeVelocity;                           //目标和自我车辆之间的相对速度
		float   OBJ_LongRelativeVelocityStd;                        //相对速度偏差
		float   OBJ_AbsoluteLatVelocityStd;                         //绝对横向速度偏差
		float   OBJ_RelativeLatVelocity;                            //相对横向速度
		float   OBJ_RelativeLatVelocityStd;                         //相对横向速度偏差
		float   OBJ_AbsoluteAccStd;                                 //目标方向上的绝对地面加速度偏差
		float   OBJ_LongRelativeAcc;                                //目标方向上的相对地面加速度
		float   OBJ_LongRelativeAccStd;                             //目标方向上的相对地面加速度偏差
		float   OBJ_AbsoluteLatAccStd;                              //目标方向上的绝对地面横向加速度偏差
		float   OBJ_RelativeLatAcc;                                 //目标方向上的相对地面横向加速度
		float   OBJ_RelativeLatAccStd;                              //目标方向上的相对地面横向加速度偏差
		float   OBJ_AngleRateStd;                                   //角速度的标准偏差。 (弧度)
		float   OBJ_ClassProbability;                               //目标类型分类的置信度
		float   OBJ_HeadingStd;                                     //偏差
		uint8_t OBJ_CameraSource;                                   // 0=Front_Left 1=Left_Rear 2=Front_Right 3=Right_Rear 4=Front_Left and Left_Rear
									                                // 5=Front_Right and Right_Rear
		uint8_t OBJ_Cipv;                                           // 0-not,1-yes
		uint8_t OBJ_LeftCipv;                                       // 0-not,1-yes
		uint8_t OBJ_RightCipv;                                      // 0-not,1-yes
		uint8_t OBJ_BrakeLight;                                     // 0-invalid 1-off 2-on
		uint8_t OBJ_TurnIndicatorRight;                             // 0-invalid 1-off 2-on
		uint8_t OBJ_TurnIndicatorLeft;                              // 0-invalid 1-off 2-on
		int32_t OBJ_PositionObjImageLane;                           // 0-UNKNOWN,1-LEFT_LEFT,2-LEFT lane,3-HOST lane,4-RIGHT lane,5-RIGHT_RIGHT
		float   OBJ_TrafficSignValue;                               //The value for each traffic sign category. for speed limit sign, values examples: 30, 50, 90
        uint64_t   CameraExposureSyncTimestamp;                        //感知摄像头曝光时间戳，用于计算延迟补偿
		uint8_t OBJ_BlockageStatus;                                 //Object受遮挡的状态
	}ARCAdas_ArcCamObjData_STRUCTURE;// object in info
	
    typedef struct {
		uint64_t     ObjectFusionID; //没有目标：ID为0
		uint64_t     ObjectVisionID;
		uint64_t     ObjectRadarID;
		uint8_t      ObjectFusionSource; // 0: radar-only; 1: vision-only; 2: fusion
		float        Long_Pos;
		float        Lat_Pos;
		float        Long_Vel; // 绝对速度
		float        Lat_Vel; // 绝对速度
		float        Long_Accel; // 绝对加速度
		float        Lat_Accel; // 绝对加速度
		float        ObjectHeading; // 归一化[-pi, pi]
		float        ObjectLength;
		float        ObjectWidth;
		uint8_t      ObjectVehicleType; //目标类型0=truck，1=car，2=pedestrain,4=rider(bike、motorbike、tricycle) 5=traffic zone
		                               //6=other car  7=bus 8=unknown 11=traffic sign
		uint8_t      ObjectMeasurementStatus; // 0: unknown; 1: new; 2: predicted; 3: measured; 
		uint8_t      ObjectMotionStatus; // 0: unknown;1: moving-forward; 2: moving-backward; 3: stationary;  
		uint8_t      ObjectCut_InOut_Flag; // 0: unknown; 1: cut-in; 2: cut-out	
		uint8_t      ObjectLocationIndex; // 0: unknown; 1: cipv; 2: sipv; 3: left1; 4: left2; 5: right1; 6: right2
		float        ObjectTTC;
		uint8_t      ObjectBlockageStatus; // Object受遮挡状态
	}ARCAdas_ArcFusionObjData_STRUCTURE;// fusion object in info


    typedef struct {
        uint8_t  Cam_Sensor_Status;
        uint8_t  Num_of_Animal;
        uint8_t  Num_of_General_Obj;
        uint8_t  Num_of_VD;
        uint8_t  Num_of_VRU;
        float    OBJ_Latency;
        uint32_t OBJ_CIPV_ID_0;                                     //最近的路径上的CIPV主目标ID
        uint32_t OBJ_CIPV_ID_1;                                     //最近的路径上的CIPV前方的目标ID
        uint32_t OBJ_CIPV_ID_2;                                     //行驶路径左侧潜在目标ID
        uint32_t OBJ_CIPV_ID_3;                                     //行驶路径右侧潜在目标ID	
        uint8_t  Rolling_Counter;
    }ARCAdas_ArcObjHeader_STRUCTURE; // object header info

    typedef struct {
		//0x275
        float   ESP_WheelSpeedRR;   
        uint8_t ESP_WheelSpeedRRValid;
		float   ESP_WheelSpeedRL;
		uint8_t ESP_WheelSpeedRLValid;  		
		//0x125
        uint8_t ESP_WheelRotationDirectionRR;
        uint8_t ESP_WheelRotationDirectionRL;
		//0x255
        float   ESP_WheelSpeedFR;     
        uint8_t ESP_WheelSpeedFRValid;
		float   ESP_WheelSpeedFL;	
		uint8_t ESP_WheelSpeedFLValid;		
		//0x105
        uint8_t ESP_WheelRotationDirectionFR;  
        uint8_t ESP_WheelRotationDirectionFL;
		//0x5FF
        uint8_t EPB_Status;    
		//0x217
        float   ESP_MasterCylinderPressure;
		//0x295
        uint8_t ESP_VDCCtrlActive;                  
        uint8_t ESP_ABSCtrlActive;  
        //0x297
        uint8_t ESP_VLC_CDDCtrlActive;        
		uint8_t ESP_CDDBrakeforceAvailable;		
        uint8_t ESP_AEBAvailable;  
		//0x215
        uint8_t IB_BrakePedalValid;   
        uint8_t IB_BrakePedalStatus; 
		//0x117
        uint8_t VCU_CH_ActualGearShiftPosition; 
		uint8_t VCU_CH_AccelPedalPositionValid;
        float   VCU_CH_AccelPedalPosition;
		uint8_t VCU_CH_ACCAvailable;
		uint8_t VCU_CH_TorqOverrideReq;
        //0x357
        uint8_t BCM_RightTurnlightStatus;       
        uint8_t BCM_LeftTurnlightStatus; 
		uint8_t BCM_RRDoorStatus;             
        uint8_t BCM_RLDoorStatus;            
        uint8_t BCM_FRDoorStatus;      
        uint8_t BCM_FLDoorStatus;		
		//0x155
        float   EPS_SteeringAngleSpeed;        
        float   EPS_SteeringAngle;   
		uint8_t EPS_ErrorFlag; 
		//0x164
		float   EPS_EPSTouqbar;
		float   EPS_TorqueResp;
		uint8_t EPS_LKAorALCCtrlStatus;                                  //新增LKA ALC控制状态
        //0x225
        float   ACU_YawRate;          
        float   ACU_LateralAcc; 
		uint8_t ACU_YawRateSensorState; 
		uint8_t ACU_LateralSensorState; 
        //0x235
        float   ACU_LongititudeAcc; 
		uint8_t ACU_LongitSensorState; 		
		//0x295
        uint8_t ESP_VehicleStandStill;         
        float   ESP_VehicleSpeed;     
		uint8_t ESP_HDCCtrlActive;        
		//0x2A5
		uint8_t TCM_ACCSpdMinusSwitchStatus;         
        uint8_t TCM_ACCSpdPlusSwitchStatus;
		uint8_t TCM_ACCCancelSwitchStatus;
		uint8_t TCM_ACCSetSwitchStatus;
		uint8_t TCM_ACCDistSwitchStatus; 
		//0x39A
		uint8_t HU_LKA_SW;
		//0x3CC
		uint8_t IPC_ALCEnableReq;                                        //ALC开关    
        uint8_t IPC_AEBEnableReq;                                        //AEB开关    
		//0x330
		uint8_t IPC_FCWEnableReq;                                        //FCW开关    
		//0x357
		uint8_t BCM_DriverSBRStatus;
		//0x330
		uint8_t RSCM_PassengerSBRStatus;
		uint8_t RSCM_RLSBRStatus;
		uint8_t RSCM_RRSBRStatus;
    } ARCAdas_EgoBusInData_STRUCTURE; // vehicle in chassis info

    // ADAS platform CAN output
    typedef struct { 
		//0x243
		uint8_t  ADAS_ABAEnable;
		uint8_t  ADAS_ABALevelReq;
		uint8_t  ADAS_ABPReq;
		uint8_t  ADAS_ACCAEBBAAct;
		float    ADAS_AEBAxTar;
		uint8_t  ADAS_AEBRequest;
		uint8_t  ADAS_AVHActiveReq;
		uint8_t  ADAS_AWBEnable;
		uint8_t  ADAS_AWBWarningLevel;
		uint8_t  ADAS_EmergencyBrakeReq;
		uint8_t  ADAS_GearsCtrlReq;
		uint8_t  ADAS_GearsTarget;
		uint8_t  ADAS_MRR_ACCMode;
		uint8_t  ADAS_TorqOverrideModeStatus;
		uint8_t  ADAS_MRR_MinimumBrake;
		uint8_t  ADAS_MRRmode;
		uint8_t  ADAS_MRRmodevalid;
		//0x323
		uint8_t  ADAS_ACCActive;
		uint8_t  ADAS_ACCAvailable;
		uint8_t  ADAS_ACCCruiseDistance;
		uint8_t  ADAS_ACCCruiseSpeed;
		uint8_t  ADAS_ACCDriveoffDisp;
		uint8_t  ADAS_ACCHoldDisp;
		uint8_t  ADAS_ACCOverRideDisp;
		uint8_t  ADAS_ACCStandby;
		uint8_t  ADAS_ACCUnAvailableWarning;
		uint8_t  ADAS_ACCVelLessThan30;
		uint8_t  ADAS_ACCVelOverLimit;
		uint8_t  ADAS_AEBActiveMode;
		uint8_t  ADAS_ECUModeState;
		uint8_t  ADAS_ExceedRelativeSpeed;
		uint8_t  ADAS_IHCFuncFeedback;
		uint8_t  ADAS_LKAFUNCFeedBack;
		uint8_t  ADAS_LKAorALCTouchWarning;
		uint8_t  ADAS_speedlimit_info;
		uint8_t  ADAS_speedlimitRemind_Info;
		uint8_t  ADAS_SR_Remind_Info;
		uint8_t  ADAS_ParkingSpaceDirection;
		uint8_t  ADAS_APODirection;
		//0x354
		uint8_t  ADAS_ACCARmndr;
		uint8_t  ADAS_AEBFuncFeedback;
		uint8_t  ADAS_ALCCrossLine;
		uint8_t  ADAS_ALCFuncFeedback;
		uint8_t  ADAS_ALCStatus;
		uint8_t  ADAS_ALCStatusup;
		uint8_t  ADAS_ALCWarning;
		uint8_t  ADAS_BCM_HazardlightReq;
		uint8_t  ADAS_ECUFaultStatus;
		uint8_t  ADAS_FaultStatus;
		uint8_t  ADAS_FCWFuncFeedback;
		uint8_t  ADAS_FCWWarning;
		uint8_t  ADAS_FusionFaultStatus;
		uint8_t  ADAS_ILSI_function_feedback;
		uint8_t  ADAS_ILSIFault;
		uint8_t  ADAS_LDWConfigFeedback;
		uint8_t  ADAS_LDWMode;
		uint8_t  ADAS_LDWWarning;
		uint8_t  ADAS_LKAAvailable;
		uint8_t  ADAS_LKAFunctionEnable;
		uint8_t  ADAS_LKAStandby;
		uint8_t  ADAS_LKATouchWarning;
		uint8_t  ADAS_LKAWarning;
		uint8_t  ADAS_SR_function_feedback;
		uint8_t  ADAS_BSDRightSoundWarning;
		uint8_t  ADAS_BSDFuncFeedback;
		uint8_t  ADAS_BSDLeftSoundWarning;
		uint8_t  ADAS_APAFuncStatus;
		uint8_t  ADAS_APFuncSelectFeedback;
		uint8_t  ADAS_APOFuncStatus;
		//0x223
		uint8_t  ADAS_ACCDriveoffReq;
		float    ADAS_VLCAxTarAim;
		float    ADAS_VLCAxTarLowerComfLimit;
		float    ADAS_VLCAxTarLowerLimit;
		float    ADAS_VLCAxTarUpperComfLimit;
		float    ADAS_VLCAxTarUpperLimit;
		uint8_t  ADAS_VLCBrakePreferred;
		uint8_t  ADAS_VLCBS_ShutdownReq;
		uint8_t  ADAS_VLCECGPOvrd;
		uint8_t  ADAS_ACCStandstillReq;
		//0x171
		uint8_t  ADAS_EPS_APCtrlReq;
		uint8_t  ADAS_EPS_APFuncReq;
		uint8_t  ADAS_LKAorALCFuncReq;
		uint8_t  ADAS_TouchDetectionStatus;
		float    ADAS_APTarSteeringAngle;
		uint8_t  ADAS_APDriverInCarStatus;
		uint8_t  ADAS_VirtualSteerWheelTorqReqValid;
		uint8_t  ADAS_VirtualSteerWheelTorqReqStatus;
		float    ADAS_VirtualSteerWheelTorqReq;
		//0x33A
		uint8_t ADAS_brake_display_with_arrow;
		uint8_t ADAS_crash_prewarning;
		uint8_t ADAS_object1_colored;
		uint8_t ADAS_object2_colored;
		uint8_t ADAS_object3_colored;
		uint8_t ADAS_object4_colored;
		uint8_t ADAS_object5_colored;
		uint8_t ADAS_object6_colored;
		uint8_t ADAS_object7_colored;
		uint8_t ADAS_object8_colored;
		uint8_t ADAS_object9_colored;
		uint8_t ADAS_Object9Type;
		float   ADAS_RelativeLateralDistToObject9;
		float   ADAS_RelativeLogitudinalDistToObject9;
		int8_t  ADAS_TruckaAvOffset;
		uint8_t ADAS_TruckAvTips;
		uint8_t ADAS_MainObjectID;
		//0x343
		uint8_t ADAS_Object1Type;
		uint8_t ADAS_Object2Type;
		float   ADAS_RelativeLateralDistToObject1;
		float   ADAS_RelativeLateralDistToObject2;
		float   ADAS_RelativeLogitudinalDistToObject1;
		float   ADAS_RelativeLogitudinalDistToObject2;
		//0x365
		uint8_t ADAS_Object3Type;
		uint8_t ADAS_Object4Type;
		float   ADAS_RelativeLateralDistToObject3;
		float   ADAS_RelativeLateralDistToObject4;
		float   ADAS_RelativeLogitudinalDistToObject3;
		float   ADAS_RelativeLogitudinalDistToObject4;
		//0x35A
		uint8_t ADAS_Object5Type;
		uint8_t ADAS_Object6Type;
		float   ADAS_RelativeLateralDistToObject5;
		float   ADAS_RelativeLateralDistToObject6;
		float   ADAS_RelativeLogitudinalDistToObject5;
		float   ADAS_RelativeLogitudinalDistToObject6;
		//0x31A
		uint8_t ADAS_Object7Type;
		uint8_t ADAS_Object8Type;
		float   ADAS_RelativeLateralDistToObject7;
		float   ADAS_RelativeLateralDistToObject8;
		float   ADAS_RelativeLogitudinalDistToObject7;
		float   ADAS_RelativeLogitudinalDistToObject8;
		//0x32E
		uint8_t ADAS_ACCExitReason;
		uint8_t ADAS_ALC_BlockObjID;
		uint8_t ADAS_ALCFunstatistical;
		uint8_t ADAS_buckle_unfasten_warning;
		uint8_t ADAS_ExceedSpdLimit;
		uint8_t ADAS_FuncRemind;
		uint8_t ADAS_IntelAdjCrusSpdTips;
		uint8_t ADAS_LaneChngWaitDisp;
		uint8_t ADAS_LeftLaneColor;
		uint8_t ADAS_LeftLaneType;
		uint8_t ADAS_LeftLeftLaneColor;
		uint8_t ADAS_LeftLeftLaneType;
		uint8_t ADAS_LKAExitReason;
		uint8_t ADAS_LKAFuncStatus;
		uint8_t ADAS_RightLaneColor;
		uint8_t ADAS_RightLaneType;
		uint8_t ADAS_RightRightLaneColor;
		uint8_t ADAS_RightRightLaneType;
		uint8_t NOA_LaneChangeCase_statis;
		//0x314
		float   ADAS_LeftLane_C0;
		float   ADAS_LeftLane_C1;
		float   ADAS_LeftLane_C2;
		float   ADAS_LeftLane_C3;
		//0x31C
		uint8_t LKA_TimeLeftQuit;
		float   ADAS_LeftLeftLane_C0;
		float   ADAS_LeftLeftLane_C1;
		float   ADAS_LeftLeftLane_C2;
		float   ADAS_LeftLeftLane_C3;
		//0x334
		float   ADAS_RightLane_C0;
		float   ADAS_RightLane_C1;
		float   ADAS_RightLane_C2;
		float   ADAS_RightLane_C3;
		//0x33C
		uint8_t ACC_TimeLeftQuit;
		float   ADAS_RightRightLane_C0;
		float   ADAS_RightRightLane_C1;
		float   ADAS_RightRightLane_C2;
		float   ADAS_RightRightLane_C3;
    } ARCAdas_ArcEgoBusOutData_STRUCTURE; // adas control out inf
	
	typedef struct { 
        uint8_t YawRateBiasValid;         //0-没有存储过，则给0，存入过有效数据则给1     
        float YawRateBiasRead;            //记忆存储的横摆角bias
        uint8_t SteerWheelAngleBiasValid; //0-没有存储过，则给0，存入过有效数据则给1   
        float SteerWheelAngleBiasRead;    //记忆存储的方向盘bias        
    } PNC_Bias_In_Structure;              // Bias读取结构体

	typedef struct { 
        uint8_t YawRateBiasWriteReq;           //0-没有存储请求   1-请求系统存储，每个点火周期记忆一次            
        float YawRateBiasWrite;                //用于记忆存储的横摆角bias   
        uint8_t SteerWheelAngleBiasWriteReq;   //0-没有存储请求   1-请求系统存储，每个点火周期记忆一次   
        float SteerWheelAngleBiasWrite;        //用于记忆存储的方向盘bias     
    } PNC_Bias_Out_Structure;             // Bias写入结构体	
	
	
	typedef struct { 
        uint64_t PNC_Cipv_Id;             
        uint64_t PNC_Sipv_Id;             
        uint64_t PNC_LeftTarget_Id;   
		uint64_t PNC_LeftTargetSec_Id;
        uint64_t PNC_RightTarget_Id;   
		uint64_t PNC_RightTargetSec_Id;
        uint64_t PNC_HazardTarget_Id;          
    } PNC_ACCTarget_Structure; // ACC目标选择

	typedef struct{
		float TarSelnPathLeftEdge_X[TarSelnPathNODE];                 //左边缘100个点的X
		float TarSelnPathLeftEdge_Y[TarSelnPathNODE];                 //左边缘100个点的Y
		float TarSelnPathRightEdge_X[TarSelnPathNODE];                //右边缘100个点的X
		float TarSelnPathRightEdge_Y[TarSelnPathNODE];                //右边缘100个点的Y
	}PNC_TarSelnPredictionPath_Structure;                             // ACC目标选择中所使用的预测轨迹
	
    // ACC VDI 显示需求信号汇总
	typedef struct{
		float    ACC_Accel_Req;
		float    ACC_AccelReq_Sipv;
		float    ACC_AccelReq_Left;
		float    ACC_AccelReq_Left2;
		float    ACC_AccelReq_Right;
		float    ACC_AccelReq_Right2;		
		float    ACC_Torque_Req;
		float    ACC_RisingJerkRate;
		float    ACC_FallingJerkRate;
		float    ACC_Decel_Req;
		uint8_t  ACC_Status;
		uint8_t  ACC_CurveSpdLimtFlag;
		uint8_t  ACC_AccSts_Internal;
		uint8_t  ACC_VehicleStandstill;
		uint8_t  ACC_WheelDirection;
		uint8_t  ACC_BrakePedalSts;
		uint8_t  ACC_DrvrOverride;
		uint8_t  ACC_CurrentSpeedLimit;
		float    ACC_TimeGapLevel;
		float    ACC_AccelReqByALead;
		float    ACC_ProcessedALead;
		uint8_t  ACC_Stop_Req;
		uint8_t  ACC_DriveOff_Req;
		uint8_t  ACC_IsTarSelnLaneBased;
		float    ACC_VelSpd;             			
		float    ACC_VelAccel;           
		uint32_t CIPVcam_ID;             
		float    CIPVcam_ObtTime;       
		float    CIPVcam_LngDis;         	
		float    CIPVcam_LngSpd;      
		float    CIPVcam_LatDis;        
		float    CIPVcam_LatSpd;         
		float    CIPVcam_LngAccel;      
		uint32_t CIPVrad_ID;            
		float    CIPVrad_ObtTime;        	
		float    CIPVrad_LngDis;        
		float    CIPVrad_LngSpd;        
		float    CIPVrad_LngVe ;        
		float    CIPVrad_LatDis;        
		float    CIPVrad_LatSpd;        
		float    CIPVrad_LatVe;         
		float    CIPVrad_LngAccel;     
		float    ACC_De;                
		float    ACC_Ve;                
		float    ACC_ThDes;             
		float    ACC_ThReal;           
		float    ACC_ThErro;           
		float    ACC_TTC;             
		float    ACC_AccelReq_SMC;    			
		float    ACC_AccelReq_ffwd;    
		float    ACC_AccelReq_SMC1;     
		float    ACC_AccelReq_SMC2;     
		float    ACC_AccelReq_SMC_cu;   
		float    ACC_AccelReq_SMC_acu;  
		float    ACC_AccelReq_SMC_os;   
		float    ACC_AccelReq_SMC_fb;    		
		float    ACC_AccelReq_SMC_fo;   
		float    ACC_AccelReq_Kit;      		
		float    ACC_AccelReq_Combn;    					
		float    ACC_AccelReq_Mid1;    					
		float    ACC_AccelReq_Mid2;    			
		float    ACC_AccelReq_Mid3;     				
		float    ACC_AccelReq_Mid4;     				
		float    ACC_AccelReq_Mid5;    							
		float    ACC_Test_1[20];           									         
	}PNC_ACCRequest_Structure; 
	
	// LCC VDI 显示需求信号汇总
	typedef struct{
		float    LCC_SteerAngle_target;
		float    LCC_SteerAngle_real;
		float    LCC_feedforward_Item;
		float    LCC_Backforward_Item;
		float    LCC_Lateral_Iterm;
		float    LCC_lateral_error_rate_Iterm;
		float    LCC_heading_error_Iterm;
		float    LCC_heading_error_rate_Iterm;
		float    LCC_Center_LateralOffset;
		float    LCC_Center_HeadingAngle;
		float    LCC_Center_Curvature;
		float    LCC_Center_CurvatureDerivative;
		float    LCC_Torque_Feedforward_raw;
		float    Torque_Feedforward_Filter;
		float    Torque_Feedforward_out;
		float    StrAngErr_Integral;
		float    StrAngErr_PItem;
		float    StrSpdErr_PItem;
		float    PredictLeftLine_C0;
		float    PredictLeftLine_C1;
		float    PredictLeftLine_C2;
		float    PredictLeftLine_C3;
		float    PredictRightLine_C0;
		float    PredictRightLine_C1;
		float    PredictRightLine_C2;
		float    PredictRightLine_C3;
		float    LeftLine_C0;
		float    LeftLine_C1;
		float    LeftLine_C2;
		float    LeftLine_C3;
		float    RightLine_C0;
		float    RightLine_C1;
		float    RightLine_C2;
		float    RightLine_C3;
		float    SelectLeftLine_C0;
		float    SelectLeftLine_C1;
		float    SelectLeftLine_C2;
		float    SelectLeftLine_C3;
		float    SelectRightLine_C0;
		float    SelectRightLine_C1;
		float    SelectRightLine_C2;
		float    SelectRightLine_C3;
		uint8_t  LCC_Mode;
		uint8_t  LCC_RoadSlopeLevel;
		uint8_t  TargetLiane_Type;
		uint8_t  LeftLine_IsRE;
		uint8_t  RightLine_IsRE;
		float    LCC_Test_1[20];           									     
	}PNC_LCCRequest_Structure; 
	
	// AEB VDI 显示需求信号汇总
	typedef struct{
		float      AEB_AEBEgoADmd;
		float      AEB_ttcThresh;
		float      FCW_ttcThresh;
		float      AEB_ttc;
		float      FCW_ttc;
		uint32_t   FCW_alertObjID;
		uint32_t   AEB_alertObjID;
		uint32_t   AEB_alertSetType;
		uint32_t   FCW_alertSetType;
		uint32_t   AEB_SuppFlag; 
		uint32_t   FCW_SuppFlag;
		uint8_t    AEB_FcwTrigFlg;
		uint8_t    AEB_AebTrigFlg;
		uint8_t    AEB_FcwActFlgPre;
		uint8_t    AEB_AebBrkActFlgPre;
		uint8_t    AEB_FcwAllwFlg;
		uint8_t    AEB_AebAllwFlg;
		uint8_t    AEB_FcwCnclFlg;
		uint8_t    AEB_AebCnclFlg;
		uint8_t    AEB_FcwActFlg;
		uint8_t    AEB_AebFctSts;
		uint8_t    AEB_AebSW;
		uint8_t    AEB_StandStillAllwFlg;
		float      AEB_Test_1[20];    
	}PNC_AEBRequest_Structure;// AEB VDI 显示需求
	
	typedef struct { 
		uint32_t alertLevelID;                // 表示该结果对应哪个配置文件。从1开始，如果是0表示无效结果。当前配置文件下，1：AEB 2：FCW。
        uint32_t alertObjID;                  // 报警锁定的目标ID，大于0为有效值。当id大于0，且抑制类别为0时，视为报警触发
        float    brakeDecelReq;               // required deceleration to avoid collision，AEB配置报警时请求的减速度，当前未实现，不可使用
        uint32_t alertSetType;                // Proceding/ego TAP/Head On/other, only one type can be configured each alert level，报警时场景类别，0：无效 1：Proceding 2：TAP 3:HeadOn 4:Crossing
                                              // FCF_SET_TYPE_VEH_UNKNOWN = 0,
                                              // FCF_SET_TYPE_VEH_PRECEDING = 1,
                                              // FCF_SET_TYPE_VEH_EGO_TAP = 2,
                                              // FCF_SET_TYPE_VEH_HEADON = 3,
                                              // FCF_SET_TYPE_VEH_CROSSING = 4
	    float    ttcThresh;                   // ttc threshold used for alert (debug only) 报警阈值，调试使用
        float    ttc;                         // actual ttc of the target (debug only) 当前锁定目标对应的真实ttc，调试使用
        uint32_t AEBSuppFlag;                 // bitwise field for different suppression reasons of AEB alerts AEB报警抑制类别
                                              // VEH_AEB_SUPP_VALIDATION                 0
                                              // VEH_AEB_SUPP_MIN_TIME_FOR_CCM_AFTER_CCM 1
                                              // VEH_AEB_SUPP_MAX_YAW_RATE_FOR_CCM       2
                                              // VEH_AEB_SUPP_MAX_STEERING_ANGLE         3
                                              // VEH_AEB_SUPP_MAX_DISTANCE               4
                                              // VEH_AEB_SUPP_MAX_WARN_LENGTH            5
                                              // VEH_AEB_SUPP_MAX_SPEED                  6
                                              // VEH_AEB_SUPP_MIN_SPEED                  7
                                              // VEH_AEB_SUPP_LATERAL_AVOIDANCE          8
                                              // VEH_AEB_SUPP_MAX_SPEED_REDUCTION        9
                                              // VEH_AEB_SUPP_MAX_REL_VEL                10
                                              // VEH_AEB_SUPP_MIN_REL_VEL                11
                                              // VEH_AEB_SUPP_SAFETY                     12
        uint32_t FCWSuppFlag;                 // bitwise field for different suppression reasons of FCW alerts FCW报警抑制类别
                                              // VEH_FCW_MAX_FCA_ENABLE_SPEED            0
                                              // VEH_FCW_MIN_FCA_ENABLE_SPEED            1
                                              // VEH_FCW_MAX_FCA_ENABLE_SPEED_HYS        2
                                              // VEH_FCW_MIN_FCA_ENABLE_SPEED_HYS        3
                                              // VEH_FCW_MAX_DISTANCE                    4
                                              // VEH_FCW_FCA_ACCEL_HALO_TIME             5
                                              // VEH_FCW_FCA_ACCL_LEVEL                  6
                                              // VEH_FCW_SUPP_WARNING_WITH_BRAKE         7
                                              // VEH_FCW_FCA_BRAKE_APPLY_DUR             8
                                              // VEH_FCW_FCA_BRAKE_APPLY_HALO_TIME       9
                                              // VEH_FCW_FCA_LIA_TIME                    10
                                              // VEH_FCW_FCAR_T_CLUTCH_HALO_TIME         11
                                              // VEH_FCW_FCA_V_DIFF                      12
        uint32_t headwayAlertType;            // indication for headway alert 扩展功能，暂未实现，不可使用
                                              // FCF_HEADWAY_ALERT_TYPE_NOWARNING = 0,
                                              // FCF_HEADWAY_ALERT_TYPE_NEARWARNING = 1
	    uint32_t HeadwaySuppFlag;             // bitwise field for different suppression reasons of headway alerts 扩展功能，暂未实现，不可使用		
                                              // VEH_FCW_HW_SUPP_ACCELERATION            0
                                              // VEH_FCW_HW_SUPP_ACCEKERATION_LENGTH     1
                                              // VEH_FCW_HW_SUPP_BRAKE                   2
                                              // VEH_FCW_HW_SUPP_BRAKE_LENGTH            3
                                              // VEH_FCW_HW_SUPP_HEADWAY_ACTIVATED       4
                                              // VEH_FCW_HW_SUPP_LATERAL_AVOIDANCE       5
                                              // VEH_FCW_HW_SUPP_REL_VEL                 6
                                              // VEH_FCW_HW_SUPP_CLUTCH                  7
                                              // VEH_FCW_HW_SUPP_MIN_TIME_BEWEEN_WARNING 8
                                              // VEH_FCW_HW_SUPP_MAX_TIME_FOR_WARNING    9
                                              // VEH_FCW_HW_SUPP_MIN_TIME_CIPV_EXISTS    10
                                              // VEH_FCW_HW_SUPP_BEYOND_SPEED_ENVELOPE   11
                                              // VEH_FCW_HW_SUPP_UNDER_SPEED_ENVELOPE    12
                                              // VEH_FCW_HW_SUPP_OVER_SPEED_ENVELOPE     13
                                              // VEH_FCW_HW_SUPP_ABOVE_MAX_DISTANCE      14
                                              // FCW_HW_SUPP_ABOVE_MAX_STEER_ANGLE       15  
	} ARCAdas_Fcf_Alert_Veh_STRUCTURE;        // FCF_ALERT_SIGNAL_VEH
	
	typedef struct { 
		uint32_t  alertLevelID;               // 表示该结果对应哪个配置文件。从1开始，如果是0表示无效结果。当前配置文件下，1：AEB 2：FCW。
        uint32_t  alertObjID;                 // 报警锁定的目标ID，大于0为有效值。当id大于0，且抑制类别为0时，视为报警触发
        uint32_t  alertSetType;               // 报警时场景类别，0：无效 1：CROSSING 2：LONGITUDINAL 3:TAP_OPPOSITE_DIRECTION 4 : TAP_SAME_DIRECTION
                                              // FCF_SET_TYPE_VRU_UNKNOWN = 0,
                                              // FCF_SET_TYPE_VRU_CROSSING = 1,
                                              // FCF_SET_TYPE_VRU_LONGITUDINAL = 2,
                                              // FCF_SET_TYPE_VRU_TAP_OPPOSITE_DIRECTION = 3,
                                              // FCF_SET_TYPE_VRU_TAP_SAME_DIRECTION = 4,
		float    ttcThresh;                   // ttc threshold used for alert (debug only) 报警阈值，调试使用
        float    ttc;                         // actual ttc of the target (debug only) 当前锁定目标对应的真实ttc，调试使用
        uint32_t AEBSuppFlag;                 // bitwise field for different suppression reasons of AEB alerts AEB报警抑制类别
											  // VRU_AEB_SUPP_ABOVE_MAX_SPEED        0
											  // VRU_AEB_SUPP_BELOW_MIN_SPEED        1
											  // VRU_AEB_SUPP_BRAKE                  2
											  // VRU_AEB_SUPP_ABOVE_MAX_YAW_SPEED    3
											  // VRU_AEB_SUPP_REVERSE_GEAR           4
											  // VRU_AEB_SUPP_ABOVE_MAX_STEER_ANGLE  5
        uint32_t FCWSuppFlag;                 // bitwise field for different suppression reasons of FCW alerts FCW报警抑制类别
											  // VRU_FCW_SUPP_ABOVE_MAX_SPEED        0
											  // VRU_FCW_SUPP_BELOW_MIN_SPEED        1
											  // VRU_FCW_SUPP_BRAKE                  2
											  // VRU_FCW_SUPP_ABOVE_MAX_YAW_SPEED    3
											  // VRU_FCW_SUPP_REVERSE_GEAR           4
											  // VRU_FCW_SUPP_ABOVE_MAX_STEER_ANGLE  5
        uint32_t bCurrInPath;                 // 目标当前处于自车行驶轨迹上 0-不在轨迹   1-在轨迹上
        uint32_t bPredInPath;                 // 目标未来处于自车行驶轨迹上	0-不在轨迹   1-在轨迹上	
    } ARCAdas_Fcf_Alert_VRU_STRUCTURE;        // FCF_ALERT_SIGNAL_VRU
	
	
	typedef struct { 
		uint8_t  AEB_Status;		          // AEB是否激活标志0-no Active  1-Active
		float    AEB_Pressure;                // AEB减速度   m/s2
		uint8_t  ACC_Status;                  // ACC是否激活标志0-no Active  1-Active
		float    ACC_Braking;                 // ACC是否在制动0-no Brake  1-Brake
		float    ACC_Deceleration;            // ACC的减速度 m/s2
    } ARCAdas_AEBOutDataForCAM_STRUCTURE;     // adas output for Camera AEB SoC	
	
	typedef struct { 
		float  ACC_debug_test[DEBUG_NUM];		
    } ARCAdas_DebugBusOutData_STRUCTURE;      // adas control out info
    // main Input struct
    typedef struct {
        ARCAdas_ArcCamLaneData_STRUCTURE      ArcCamLaneData_s[LINE_MAX_NUM];
        // ARCAdas_ArcCamRdEdgeData_STRUCTURE  ArcCamRdEdgeData_s[RE_MAX_NUM];
        ARCAdas_ArcCamObjData_STRUCTURE       ArcCamObjData_s[Obj_MAX_NUM];
		ARCAdas_ArcFusionObjData_STRUCTURE    ArcACCTargetInData_s[ACC_TARGET_NUM];
        ARCAdas_EgoBusInData_STRUCTURE        ArcEgoBusInData_s;
        ARCAdas_ArcObjHeader_STRUCTURE        ArcObjHeaderInData_s;
        ARCAdas_ArcCamHeader_STRUCTURE        ArcCamHeaderInData_s;
		ARCAdas_Fcf_Alert_Veh_STRUCTURE       Arc_AEB_FCW_VehData_s[FCF_MAX_NUM_VEH];    //感知车辆的AEB flag
		ARCAdas_Fcf_Alert_VRU_STRUCTURE       Arc_AEB_FCW_VRUData_s[FCF_MAX_NUM_VRU];	 //感知行人的AEB flag	
		PNC_Bias_In_Structure                 Arc_Bias_In;                               //读取bias
    } ARCAdas_FUNCTION_In_STRUCTURE;

    // main Output struct
    typedef struct {
        ARCAdas_ArcEgoBusOutData_STRUCTURE    ArcEgoBusOutData;
        ARCAdas_DebugBusOutData_STRUCTURE     ArcDebugBusOutData;
	    ARCAdas_AEBOutDataForCAM_STRUCTURE    ArcAEBOutDataForCAM;                       //规控AEB输出给感知
		PNC_ACCTarget_Structure               ArcPNC_Target;                             //ACC目标 VDI显示
		PNC_TarSelnPredictionPath_Structure   ArcPNC_TarSelnPredictionPath;              //ACC预测轨迹 VDI显示
		PNC_ACCRequest_Structure              ArcPNC_ACCRequest;                         //ACC状态请求 VDI显示
		PNC_LCCRequest_Structure              ArcPNC_LCCRequest;
		PNC_AEBRequest_Structure              ArcPNC_AEBRequest;
		uint32_t                              FrameIdOut;                                // 用于VDI录制当前规控对应的图像输入帧Id
		PNC_Bias_Out_Structure                Arc_Bias_Out;                              //写入bias
    } ARCAdas_FUNCTION_Out_STRUCTURE;


    typedef void* pAdasEngine;

    ARC_DLL int ArcAdas_Init(pAdasEngine* pHandle);

    ARC_DLL int ArcAdas_Step(pAdasEngine* pHandle, ARCAdas_FUNCTION_In_STRUCTURE* pInputParam, ARCAdas_FUNCTION_Out_STRUCTURE* pOutResult);

    ARC_DLL int ArcAdas_UnInit(pAdasEngine* pHandle);

    // version info
    typedef struct {
        long long lCodebase;             	 // Codebase version number
        long long lMajor;                	 // major version number
        long long lMinor;                	 // minor version number
        long long lBuild;                	 // Build version number, increasable only
        const char* Version;        	     // version in string form
        const char* BuildDate;      	     // latest build Date
        const char* CopyRight;      	     // copyright
    } ArcAdas_Version;

    ARC_DLL const ArcAdas_Version* ArcAdas_GetVersion();

#ifdef __cplusplus
}
#endif


#endif //_ARCSOFT_ADAS_LKA_H_
