/*
 * File: ArcAdas_EmbeddedCoder_Frame_types.h
 *
 * Code generated for Simulink model 'ArcAdas_EmbeddedCoder_Frame'.
 *
 * Model version                  : 7.2225
 * Simulink Coder version         : 9.6 (R2021b) 14-May-2021
 * C/C++ source code generated on : Thu Jul  3 17:31:36 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Custom Processor
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_ArcAdas_EmbeddedCoder_Frame_types_h_
#define RTW_HEADER_ArcAdas_EmbeddedCoder_Frame_types_h_
#include "rtwtypes.h"

/* Model Code Variants */
#ifndef DEFINED_TYPEDEF_FOR_Arc_Line_
#define DEFINED_TYPEDEF_FOR_Arc_Line_

typedef struct {
  /* line ID */
  uint8_T Line_Track_ID;

  /* the confidence probability of line */
  real32_T Line_Exist_Probability;
  uint8_T Line_Crossing;

  /* type of regular line */
  uint8_T Line_Type_Class;

  /* start position of line range */
  real32_T Line_View_Range_Start;

  /* end position of line range */
  real32_T Line_View_Range_End;

  /* color of line */
  uint8_T Line_Color;
  uint8_T ArcCam_Line_Age;
  real32_T Line_Marker_Width;
  real32_T Dash_Average_Length;
  real32_T Dash_Average_Gap;
  uint8_T Prediction_Source;
  real32_T Line_C0;
  real32_T Line_C1;
  real32_T Line_C2;
  real32_T Line_C3;
} Arc_Line;

#endif

#ifndef DEFINED_TYPEDEF_FOR_Arc_FC_Cam_Header_
#define DEFINED_TYPEDEF_FOR_Arc_FC_Cam_Header_

typedef struct {
  /* time stamp of camera detection */
  real32_T FC_Cam_Time_Stamp;
} Arc_FC_Cam_Header;

#endif

#ifndef DEFINED_TYPEDEF_FOR_Arc_Objects_Header_
#define DEFINED_TYPEDEF_FOR_Arc_Objects_Header_

typedef struct {
  /* camera status */
  uint8_T Cam_Sensor_Status;

  /* animal number */
  uint8_T Num_of_Animal;

  /* objects number */
  uint8_T Num_of_General_Obj;
  uint8_T Num_of_VD;

  /* VRU number */
  uint8_T Num_of_VRU;

  /* id of closest in path object */
  uint8_T OBJ_VD_CIPV_ID;

  /* object latency */
  real32_T OBJ_Latency;

  /* rolling counter of the message */
  uint8_T Rolling_Counter;

  /* left target */
  uint8_T OBJ_VD_LeftTarget_ID;

  /* right target */
  uint8_T OBJ_VD_RightTarget_ID;

  /* second in path target */
  uint8_T OBJ_VD_SIPV_ID;
} Arc_Objects_Header;

#endif

#ifndef DEFINED_TYPEDEF_FOR_Arc_ObjectData_
#define DEFINED_TYPEDEF_FOR_Arc_ObjectData_

typedef struct {
  /* object id */
  uint64_T OBJ_ID;

  /* confidence probability */
  real32_T OBJ_Existence_Probability;

  /* motion pattern */
  uint8_T OBJ_Motion_Category;

  /* object age */
  uint8_T OBJ_Object_Age;

  /* obj measurent status */
  uint8_T OBJ_Measuring_Status;

  /* object class */
  uint8_T OBJ_Object_Class;

  /* object motion status */
  uint8_T OBJ_Motion_Status;

  /* object brake light */
  uint8_T OBJ_Brake_Light;

  /* object right-turn indicator */
  uint8_T OBJ_Turn_Indicator_Right;

  /* object left-turn indicator */
  uint8_T OBJ_Turn_Indicator_Left;
  real32_T OBJ_Length;
  real32_T OBJ_Width;
  real32_T OBJ_Abs_Long_Velocity;
  real32_T OBJ_Abs_Lat_Velocity;
  real32_T OBJ_Abs_Long_Acc;
  real32_T OBJ_Heading_Angle;
  real32_T OBJ_Long_Distance;
  real32_T OBJ_Abs_Lat_Acc;
  real32_T OBJ_Lat_Distance;
  real32_T OBJ_Angle_Rate;
  real32_T OBJ_Inv_TTC;
  real32_T OBJ_WidthStd;
  real32_T OBJ_LengthStd;
  real32_T OBJ_Height;
  real32_T OBJ_HeightStd;
  real32_T OBJ_LongDistanceStd;
  real32_T OBJ_LatDistanceStd;
  real32_T OBJ_AbsLongVelocityStd;
  real32_T OBJ_LongRelativeVelocity;
  real32_T OBJ_LongRelativeVelocityStd;
  real32_T OBJ_AbsoluteLatVelocityStd;
  real32_T OBJ_RelativeLatVelocity;
  real32_T OBJ_RelativeLatVelocityStd;
  real32_T OBJ_AbsoluteAccStd;
  real32_T OBJ_LongRelativeAcc;
  real32_T OBJ_LongRelativeAccStd;
  real32_T OBJ_AbsoluteLatAccStd;
  real32_T OBJ_RelativeLatAcc;
  real32_T OBJ_RelativeLatAccStd;
  real32_T OBJ_AngleRateStd;
  real32_T OBJ_ClassProbability;
  real32_T OBJ_HeadingStd;
  uint8_T OBJ_CameraSource;
  uint8_T OBJ_Cipv;
  uint8_T OBJ_LeftCipv;
  uint8_T OBJ_RightCipv;
  int32_T OBJ_PositionObjImageLane;
  real32_T OBJ_TrafficSignValue;
  uint8_T OBJ_BlockageStatus;
} Arc_ObjectData;

#endif

#ifndef DEFINED_TYPEDEF_FOR_Arc_RearAEB_ObjectDate_
#define DEFINED_TYPEDEF_FOR_Arc_RearAEB_ObjectDate_

typedef struct {
  uint8_T OBJ_ID;
  real32_T OBJ_Existence_Probability;
  uint8_T OBJ_Motion_Category;
  uint16_T OBJ_Object_Age;
  uint8_T OBJ_Measuring_Status;
  uint8_T OBJ_Object_Class;
  uint8_T OBJ_Motion_Status;
  real32_T OBJ_Length;
  real32_T OBJ_Width;
  real32_T OBJ_Abs_Long_Velocity;
  real32_T OBJ_Abs_Lat_Velocity;
  real32_T OBJ_Abs_Long_Acc;
  real32_T OBJ_Heading_Angle;
  real32_T OBJ_Long_Distance;
  real32_T OBJ_Abs_Lat_Acc;
  real32_T OBJ_Lat_Distance;
  real32_T OBJ_Angle_Rate;
  real32_T OBJ_Inv_TTC;
} Arc_RearAEB_ObjectDate;

#endif

#ifndef DEFINED_TYPEDEF_FOR_Arc_RearAEB_ObjectHeader_
#define DEFINED_TYPEDEF_FOR_Arc_RearAEB_ObjectHeader_

typedef struct {
  uint8_T Cam_Sensor_Status;
  uint8_T Num_of_Animal;
  uint8_T Num_of_General_Obj;
  uint8_T Num_of_VD;
  uint8_T Num_of_VRU;
  uint8_T OBJ_VD_REAR_CIPV_ID;
  uint16_T OBJ_Latency;
  uint8_T Rolling_Counter;
} Arc_RearAEB_ObjectHeader;

#endif

#ifndef DEFINED_TYPEDEF_FOR_Arc_Fcf_Alert_Veh_Structure_
#define DEFINED_TYPEDEF_FOR_Arc_Fcf_Alert_Veh_Structure_

typedef struct {
  uint32_T alertLevelID_Veh;
  uint32_T alertObjID_Veh;
  real32_T brakeDecelReq_Veh;
  uint32_T alertSetType_Veh;
  real32_T ttcThresh_Veh;
  real32_T ttc_Veh;
  uint32_T AEBSuppFlag_Veh;
  uint32_T FCWSuppFlag_Veh;
  uint32_T headwayAlertType_Veh;
  uint32_T HeadwaySuppFlag_Veh;
} Arc_Fcf_Alert_Veh_Structure;

#endif

#ifndef DEFINED_TYPEDEF_FOR_Arc_Fcf_Alert_VRU_Structure_
#define DEFINED_TYPEDEF_FOR_Arc_Fcf_Alert_VRU_Structure_

typedef struct {
  uint32_T alertLevelID_VRU;
  uint32_T alertObjID_VRU;
  uint32_T alertSetType_VRU;
  real32_T ttcThresh_VRU;
  real32_T ttc_VRU;
  uint32_T AEBSuppFlag_VRU;
  uint32_T FCWSuppFlag_VRU;
  uint32_T bCurrInPath_VRU;
  uint32_T bPredInPath_VRU;
} Arc_Fcf_Alert_VRU_Structure;

#endif

#ifndef DEFINED_TYPEDEF_FOR_FusionObject_
#define DEFINED_TYPEDEF_FOR_FusionObject_

typedef struct {
  /* fusion object id */
  uint64_T ObjectFusionID;

  /* vision-id of this fusion object */
  uint64_T ObjectVisionID;

  /* radar-id of this fusion object */
  uint64_T ObjectRadarID;

  /* : radar-only; 1: vision-only; 2: fusion */
  uint8_T ObjectFusionSource;

  /* longi-distance */
  real32_T Long_Pos;

  /* lat-distance */
  real32_T Lat_Pos;

  /* longi-speed, mps */
  real32_T Long_Vel;

  /* lat-speed, mps */
  real32_T Lat_Vel;

  /* longi-accel, mpss */
  real32_T Long_Accel;

  /* lat-accel, mpss */
  real32_T Lat_Accel;

  /* heading in pi */
  real32_T ObjectHeading;

  /* length */
  real32_T ObjectLength;

  /* width */
  real32_T ObjectWidth;

  /* vehicle type */
  uint8_T ObjectVehicleType;

  /* 0: unknown; 1: new; 2: predicted; 3: measured */
  uint8_T ObjectMeasurementStatus;

  /* 0: unknown;1: moving-forward; 2: moving-backward; 3: stationary;   */
  uint8_T ObjectMotionStatus;

  /* 0: unknown; 1: cut-in; 2: cut-out	 */
  uint8_T ObjectCut_InOut_Flag;

  /* 0: unknown; 1: cipv; 2: sipv; 3: left1; 4: left2; 5: right1; 6: right2 */
  uint8_T ObjectLocationIndex;

  /* time-to-collision */
  real32_T ObjectTTC;

  /* the status of objects blockage */
  uint8_T ObjectBlockageStatus;
} FusionObject;

#endif

#ifndef DEFINED_TYPEDEF_FOR_ACC_debug_out_
#define DEFINED_TYPEDEF_FOR_ACC_debug_out_

typedef struct {
  real32_T ACC_debug_test_001;
  real32_T ACC_debug_test_002;
  real32_T ACC_debug_test_003;
  real32_T ACC_debug_test_004;
  real32_T ACC_debug_test_005;
  real32_T ACC_debug_test_006;
  real32_T ACC_debug_test_007;
  real32_T ACC_debug_test_008;
  real32_T ACC_debug_test_009;
  real32_T ACC_debug_test_010;
  real32_T ACC_debug_test_011;
  real32_T ACC_debug_test_012;
  real32_T ACC_debug_test_013;
  real32_T ACC_debug_test_014;
  real32_T ACC_debug_test_015;
  real32_T ACC_debug_test_016;
} ACC_debug_out;

#endif

#ifndef DEFINED_TYPEDEF_FOR_ACCVehChassis_
#define DEFINED_TYPEDEF_FOR_ACCVehChassis_

typedef struct {
  uint8_T DrvingDirection;
  real32_T VehSpd;
  real32_T DispVehSpd;
  uint8_T EPBCtrlActive;
  uint8_T VehicleStandStill;
  uint8_T BrakePedalStatus;
  real32_T AccelPedalPosition;
  uint8_T ActualGearShiftPosition;
  uint8_T TorqOverrideReq;
  real32_T SteeringAngle;
  real32_T LateralAcc;
  real32_T YawRate;
  real32_T LongititudeAcc;
  uint8_T ACCSetSwitchStatus;
  uint8_T ACCCancelSwitchStatus;
  uint8_T ACCSpdPlusSwitchStatus;
  uint8_T ACCSpdMinusSwitchStatus;
  uint8_T ACCDistSwitchStatus;
  uint8_T CDDActv;
  uint8_T LeftTurnIndicator;
  uint8_T RightTurnIndicator;
} ACCVehChassis;

#endif

#ifndef DEFINED_TYPEDEF_FOR_AccTgt_
#define DEFINED_TYPEDEF_FOR_AccTgt_

typedef struct {
  /* acc target id */
  uint64_T AccTgt_id;

  /* acc target longitudinal distance, m */
  real32_T AccTgt_x_dist;

  /* acc target lateral distance, m */
  real32_T AccTgt_y_dist;

  /* acc target longitudinal speed, mps */
  real32_T AccTgt_x_spd;

  /* acc target lateral speed, mps */
  real32_T AccTgt_y_spd;

  /* acc target heading angle */
  real32_T AccTgt_headingangle;

  /* acc target longitudinal acceleration, mpss */
  real32_T AccTgt_x_accel;

  /* acc target lateral acceleration, mpss */
  real32_T AccTgt_y_accel;

  /* acc target type */
  uint8_T AccTgt_type;

  /* acc target exist probability */
  real32_T AccTgt_existprob;

  /* sensor error indication */
  uint8_T sensor_ErrorSts;

  /* the status of blockage */
  uint8_T AccTgt_BlockageStatus;

  /* the status of blockage */
  uint8_T AccTgt_CutinCutoutStatus;
} AccTgt;

#endif

#ifndef DEFINED_TYPEDEF_FOR_SpdPlanner_Accel_Req_Info_
#define DEFINED_TYPEDEF_FOR_SpdPlanner_Accel_Req_Info_

typedef struct {
  real32_T UpperJerkLimit;
  real32_T SpdPlanner_Accel_Req;
  real32_T LowerJerkLimit;
} SpdPlanner_Accel_Req_Info;

#endif

#ifndef DEFINED_TYPEDEF_FOR_AEB_debug_out_
#define DEFINED_TYPEDEF_FOR_AEB_debug_out_

typedef struct {
  real32_T AEB_debug_test_001;
  real32_T AEB_debug_test_002;
  real32_T AEB_debug_test_003;
  real32_T AEB_debug_test_004;
  real32_T AEB_debug_test_005;
  real32_T AEB_debug_test_006;
  real32_T AEB_debug_test_007;
  real32_T AEB_debug_test_008;
  real32_T AEB_debug_test_009;
  real32_T AEB_debug_test_010;
  real32_T AEB_debug_test_011;
  real32_T AEB_debug_test_012;
  real32_T AEB_debug_test_013;
  real32_T AEB_debug_test_014;
  real32_T AEB_debug_test_015;
  real32_T AEB_debug_test_016;
} AEB_debug_out;

#endif

#ifndef DEFINED_TYPEDEF_FOR_AEB_ObjectAdaptor_
#define DEFINED_TYPEDEF_FOR_AEB_ObjectAdaptor_

typedef struct {
  uint8_T ObjAdpr_Id;
  uint8_T ObjAdpr_Type;
  real_T ObjAdpr_ExistProb;
  uint8_T ObjAdpr_MesureSts;
  uint8_T ObjAdpr_MtnClass;
  uint8_T ObjAdpr_MtnDir;
  real_T ObjAdpr_Age;
  real_T ObjAdpr_CourseAg;
  real_T ObjAdpr_Width;
  real_T ObjAdpr_Length;
  real_T ObjAdpr_Ttc;
  uint8_T ObjAdpr_SenDefn;
  boolean_T ObjAdpr_VldFlg;
  real_T ObjAdpr_Vx;
  real_T ObjAdpr_AbsV;
  real_T ObjAdpr_AbsAcc;
  real_T ObjAdpr_X;
  real_T ObjAdpr_Y;
  real_T ObjAdpr_EgoOvlp;
  real_T ObjAdpr_EgoPredImpLoc;
  real_T ObjAdpr_VxRel;
  real_T ObjAdpr_Vy;
  uint8_T ObjAdpr_CamSenrSts;
  real_T ObjAdpr_YRaw;
  real_T COI_AEB_ObjAbsLongAcc;
} AEB_ObjectAdaptor;

#endif

#ifndef DEFINED_TYPEDEF_FOR_AEB_PerceptionSignal_Bus_
#define DEFINED_TYPEDEF_FOR_AEB_PerceptionSignal_Bus_

typedef struct {
  uint32_T alertLevelID_AEB;
  uint32_T alertObjID_AEB;
  real32_T brakeDecelReq_AEB;
  uint32_T alertSetType_AEB;
  real32_T ttcThresh_AEB;
  real32_T ttc_AEB;
  uint32_T AEBSuppFlag_AEB;
  uint32_T FCWSuppFlag_AEB;
  uint32_T headwayAlertType_AEB;
  uint32_T HeadwaySuppFlag_AEB;
  uint32_T bCurrInPath_AEB;
  uint32_T bPredInPath_AEB;
  uint8_T TargetObj_Type;
} AEB_PerceptionSignal_Bus;

#endif

#ifndef DEFINED_TYPEDEF_FOR_FCW_PerceptionSignal_Bus_
#define DEFINED_TYPEDEF_FOR_FCW_PerceptionSignal_Bus_

typedef struct {
  uint32_T alertLevelID_FCW;
  uint32_T alertObjID_FCW;
  uint32_T alertSetType_FCW;
  real32_T ttcThresh_FCW;
  real32_T ttc_FCW;
  uint32_T AEBSuppFlag_FCW;
  uint32_T FCWSuppFlag_FCW;
  uint32_T headwayAlertType_FCW;
  uint32_T HeadwaySuppFlag_FCW;
  uint32_T bCurrInPath_FCW;
  uint32_T bPredInPath_FCW;
} FCW_PerceptionSignal_Bus;

#endif

#ifndef DEFINED_TYPEDEF_FOR_AEB_Sence_Def_
#define DEFINED_TYPEDEF_FOR_AEB_Sence_Def_

typedef struct {
  uint8_T COI_AEB_OBJ_VEHICLE_STATIONARY_SAME_EGO_STRAIGHT;
  uint8_T COI_AEB_OBJ_VEHICLE_STATIONARY_ONCOMING_EGO_STRAIGHT;
  uint8_T COI_AEB_OBJ_VEHICLE_STATIONARY_CROSSING_EGO_STRAIGHT;
  uint8_T COI_AEB_OBJ_VEHICLE_STATIONARY_SAME_EGO_TURNLEFT;
  uint8_T COI_AEB_OBJ_VEHICLE_STATIONARY_ONCOMING_EGO_TURNLEFT;
  uint8_T COI_AEB_OBJ_VEHICLE_STATIONARY_CROSSING_EGO_TURNLEFT;
  uint8_T COI_AEB_OBJ_VEHICLE_STATIONARY_SAME_EGO_TURNRIGHT;
  uint8_T COI_AEB_OBJ_VEHICLE_STATIONARY_ONCOMING_EGO_TURNRIGHT;
  uint8_T COI_AEB_OBJ_VEHICLE_STATIONARY_CROSSING_EGO_TURNRIGHT;
  uint8_T COI_AEB_OBJ_VEHICLE_MOVING_SAME_EGO_STRAIGHT;
  uint8_T COI_AEB_OBJ_VEHICLE_MOVING_ONCOMING_EGO_STRAIGHT;
  uint8_T COI_AEB_OBJ_VEHICLE_MOVING_CROSSING_EGO_STRAIGHT;
  uint8_T COI_AEB_OBJ_VEHICLE_MOVING_SAME_EGO_TURNLEFT;
  uint8_T COI_AEB_OBJ_VEHICLE_MOVING_ONCOMING_EGO_TURNLEFT;
  uint8_T COI_AEB_OBJ_VEHICLE_MOVING_CROSSING_EGO_TURNLEFT;
  uint8_T COI_AEB_OBJ_VEHICLE_MOVING_SAME_EGO_TURNRIGHT;
  uint8_T COI_AEB_OBJ_VEHICLE_MOVING_ONCOMING_EGO_TURNRIGHT;
  uint8_T COI_AEB_OBJ_VEHICLE_MOVING_CROSSING_EGO_TURNRIGHT;
  uint8_T COI_AEB_OBJ_CYCLIST_STATIONARY_SAME_EGO_STRAIGHT;
  uint8_T COI_AEB_OBJ_CYCLIST_STATIONARY_ONCOMING_EGO_STRAIGHT;
  uint8_T COI_AEB_OBJ_CYCLIST_STATIONARY_CROSSING_EGO_STRAIGHT;
  uint8_T COI_AEB_OBJ_CYCLIST_STATIONARY_SAME_EGO_TURNLEFT;
  uint8_T COI_AEB_OBJ_CYCLIST_STATIONARY_ONCOMING_EGO_TURNLEFT;
  uint8_T COI_AEB_OBJ_CYCLIST_STATIONARY_CROSSING_EGO_TURNLEFT;
  uint8_T COI_AEB_OBJ_CYCLIST_STATIONARY_SAME_EGO_TURNRIGHT;
  uint8_T COI_AEB_OBJ_CYCLIST_STATIONARY_ONCOMING_EGO_TURNRIGHT;
  uint8_T COI_AEB_OBJ_CYCLIST_STATIONARY_CROSSING_EGO_TURNRIGHT;
  uint8_T COI_AEB_OBJ_CYCLIST_MOVING_SAME_EGO_STRAIGHT;
  uint8_T COI_AEB_OBJ_CYCLIST_MOVING_ONCOMING_EGO_STRAIGHT;
  uint8_T COI_AEB_OBJ_CYCLIST_MOVING_CROSSING_EGO_STRAIGHT;
  uint8_T COI_AEB_OBJ_CYCLIST_MOVING_SAME_EGO_TURNLEFT;
  uint8_T COI_AEB_OBJ_CYCLIST_MOVING_ONCOMING_EGO_TURNLEFT;
  uint8_T COI_AEB_OBJ_CYCLIST_MOVING_CROSSING_EGO_TURNLEFT;
  uint8_T COI_AEB_OBJ_CYCLIST_MOVING_SAME_EGO_TURNRIGHT;
  uint8_T COI_AEB_OBJ_CYCLIST_MOVING_ONCOMING_EGO_TURNRIGHT;
  uint8_T COI_AEB_OBJ_CYCLIST_MOVING_CROSSING_EGO_TURNRIGHT;
  uint8_T COI_AEB_OBJ_PTW_STATIONARY_SAME_EGO_STRAIGHT;
  uint8_T COI_AEB_OBJ_PTW_STATIONARY_ONCOMING_EGO_STRAIGHT;
  uint8_T COI_AEB_OBJ_PTW_STATIONARY_CROSSING_EGO_STRAIGHT;
  uint8_T COI_AEB_OBJ_PTW_STATIONARY_SAME_EGO_TURNLEFT;
  uint8_T COI_AEB_OBJ_PTW_STATIONARY_ONCOMING_EGO_TURNLEFT;
  uint8_T COI_AEB_OBJ_PTW_STATIONARY_CROSSING_EGO_TURNLEFT;
  uint8_T COI_AEB_OBJ_PTW_STATIONARY_SAME_EGO_TURNRIGHT;
  uint8_T COI_AEB_OBJ_PTW_STATIONARY_ONCOMING_EGO_TURNRIGHT;
  uint8_T COI_AEB_OBJ_PTW_STATIONARY_CROSSING_EGO_TURNRIGHT;
  uint8_T COI_AEB_OBJ_PTW_MOVING_SAME_EGO_STRAIGHT;
  uint8_T COI_AEB_OBJ_PTW_MOVING_ONCOMING_EGO_STRAIGHT;
  uint8_T COI_AEB_OBJ_PTW_MOVING_CROSSING_EGO_STRAIGHT;
  uint8_T COI_AEB_OBJ_PTW_MOVING_SAME_EGO_TURNLEFT;
  uint8_T COI_AEB_OBJ_PTW_MOVING_ONCOMING_EGO_TURNLEFT;
  uint8_T COI_AEB_OBJ_PTW_MOVING_CROSSING_EGO_TURNLEFT;
  uint8_T COI_AEB_OBJ_PTW_MOVING_SAME_EGO_TURNRIGHT;
  uint8_T COI_AEB_OBJ_PTW_MOVING_ONCOMING_EGO_TURNRIGHT;
  uint8_T COI_AEB_OBJ_PTW_MOVING_CROSSING_EGO_TURNRIGHT;
  uint8_T COI_AEB_OBJ_PED_STATIONARY_SAME_EGO_STRAIGHT;
  uint8_T COI_AEB_OBJ_PED_STATIONARY_ONCOMING_EGO_STRAIGHT;
  uint8_T COI_AEB_OBJ_PED_STATIONARY_CROSSING_EGO_STRAIGHT;
  uint8_T COI_AEB_OBJ_PED_STATIONARY_SAME_EGO_TURNLEFT;
  uint8_T COI_AEB_OBJ_PED_STATIONARY_ONCOMING_EGO_TURNLEFT;
  uint8_T COI_AEB_OBJ_PED_STATIONARY_CROSSING_EGO_TURNLEFT;
  uint8_T COI_AEB_OBJ_PED_STATIONARY_SAME_EGO_TURNRIGHT;
  uint8_T COI_AEB_OBJ_PED_STATIONARY_ONCOMING_EGO_TURNRIGHT;
  uint8_T COI_AEB_OBJ_PED_STATIONARY_CROSSING_EGO_TURNRIGHT;
  uint8_T COI_AEB_OBJ_PED_MOVING_SAME_EGO_STRAIGHT;
  uint8_T COI_AEB_OBJ_PED_MOVING_ONCOMING_EGO_STRAIGHT;
  uint8_T COI_AEB_OBJ_PED_MOVING_CROSSING_EGO_STRAIGHT;
  uint8_T COI_AEB_OBJ_PED_MOVING_SAME_EGO_TURNLEFT;
  uint8_T COI_AEB_OBJ_PED_MOVING_ONCOMING_EGO_TURNLEFT;
  uint8_T COI_AEB_OBJ_PED_MOVING_CROSSING_EGO_TURNLEFT;
  uint8_T COI_AEB_OBJ_PED_MOVING_SAME_EGO_TURNRIGHT;
  uint8_T COI_AEB_OBJ_PED_MOVING_ONCOMING_EGO_TURNRIGHT;
  uint8_T COI_AEB_OBJ_PED_MOVING_CROSSING_EGO_TURNRIGHT;
} AEB_Sence_Def;

#endif

#ifndef DEFINED_TYPEDEF_FOR_AEB_Obj_Class_
#define DEFINED_TYPEDEF_FOR_AEB_Obj_Class_

typedef struct {
  uint8_T COI_AEB_VEHICLE_OBJECT;
  uint8_T COI_AEB_TRUCK_OBJECT;
  uint8_T COI_AEB_CYCLIST_OBJECT;
  uint8_T COI_AEB_PTW_OBJECT;
  uint8_T COI_AEB_PED_OBJECT;
} AEB_Obj_Class;

#endif

#ifndef DEFINED_TYPEDEF_FOR_AEB_Obj_MotionSts_
#define DEFINED_TYPEDEF_FOR_AEB_Obj_MotionSts_

typedef struct {
  uint8_T COI_AEB_STATIONARY_OBJECT;
  uint8_T COI_AEB_MOVING_OBJECT;
} AEB_Obj_MotionSts;

#endif

#ifndef DEFINED_TYPEDEF_FOR_AEB_Obj_MtnCtgry_
#define DEFINED_TYPEDEF_FOR_AEB_Obj_MtnCtgry_

typedef struct {
  uint8_T COI_AEB_SAME_DIRECTION;
  uint8_T COI_AEB_CROSSING_DIRECTION;
  uint8_T COI_AEB_ONCOMING_DIRECTION;
} AEB_Obj_MtnCtgry;

#endif

#ifndef DEFINED_TYPEDEF_FOR_AEB_Ego_DriveSts_
#define DEFINED_TYPEDEF_FOR_AEB_Ego_DriveSts_

typedef struct {
  int8_T COI_AEB_EGO_STRAIGHT;
  int8_T COI_AEB_EGO_TURNLEFT;
  int8_T COI_AEB_EGO_TURNRIGHT;
} AEB_Ego_DriveSts;

#endif

#ifndef DEFINED_TYPEDEF_FOR_LCC_debug_out_
#define DEFINED_TYPEDEF_FOR_LCC_debug_out_

typedef struct {
  real32_T LCC_debug_test_001;
  real32_T LCC_debug_test_002;
  real32_T LCC_debug_test_003;
  real32_T LCC_debug_test_004;
  real32_T LCC_debug_test_005;
  real32_T LCC_debug_test_006;
  real32_T LCC_debug_test_007;
  real32_T LCC_debug_test_008;
  real32_T LCC_debug_test_009;
  real32_T LCC_debug_test_010;
  real32_T LCC_debug_test_011;
  real32_T LCC_debug_test_012;
  real32_T LCC_debug_test_013;
  real32_T LCC_debug_test_014;
  real32_T LCC_debug_test_015;
  real32_T LCC_debug_test_016;
} LCC_debug_out;

#endif

#ifndef DEFINED_TYPEDEF_FOR_Vehicle_Signal_
#define DEFINED_TYPEDEF_FOR_Vehicle_Signal_

typedef struct {
  real_T EVI_LKA_EgoVFil;
  real_T EVI_LKA_EgoAFil;
  uint8_T EVI_LKA_EgoAAct;
  real_T EVI_LKA_EgoALatFil;
  uint8_T EVI_LKA_GearPos;
  real_T EVI_LKA_StrWhlAg;
  real_T EVI_LKA_YawRate;
  boolean_T EVI_LKA_DoorOpen;
  real_T EVI_LKA_EgoDrvrAcclPdlPrcnt;
  boolean_T EVI_LKA_VehSysFlt;
  uint8_T EVI_LKA_TurnLight_Left;
  uint8_T EVI_LKA_TurnLight_Right;
  uint8_T EVI_LKA_Hazard_Light;
  real_T EVI_LKA_StrWhlTorque;
  real_T EVI_LKA_EgoDrvrBrakePdlPrcnt;
  uint8_T EVI_LKA_IVI_LCC_Switch;
  uint8_T EVI_LKA_Switch;
} Vehicle_Signal;

#endif

#ifndef DEFINED_TYPEDEF_FOR_NextLeftLine_Parameter_
#define DEFINED_TYPEDEF_FOR_NextLeftLine_Parameter_

typedef struct {
  real_T NextLeftLine_Exist_Probability;
  uint8_T NextLeftLine_Type_Class;
  real_T NextLeftLine_ViewRange_Start;
  real_T NextLeftLine_ViewRange_end;
  real_T NextLeftLine_C0;
  real_T NextLeftLine_C1;
  real_T NextLeftLine_C2;
  real_T NextLeftLine_C3;
} NextLeftLine_Parameter;

#endif

#ifndef DEFINED_TYPEDEF_FOR_NextRightLine_Parameter_
#define DEFINED_TYPEDEF_FOR_NextRightLine_Parameter_

typedef struct {
  real_T NextRightLine_Exist_Probability;
  uint8_T NextRightLine_Type_Class;
  real_T NextRightLine_ViewRange_Start;
  real_T NextRightLine_ViewRange_end;
  real_T NextRightLine_C0;
  real_T NextRightLine_C1;
  real_T NextRightLine_C2;
  real_T NextRightLine_C3;
} NextRightLine_Parameter;

#endif

#ifndef DEFINED_TYPEDEF_FOR_LeftLine_Parameter_
#define DEFINED_TYPEDEF_FOR_LeftLine_Parameter_

typedef struct {
  real_T LeftLine_Exist_Probability;
  uint8_T LeftLine_Type_Class;
  real_T LeftLine_ViewRange_Start;
  real_T LeftLine_ViewRange_end;
  real_T LeftLine_C0;
  real_T LeftLine_C1;
  real_T LeftLine_C2;
  real_T LeftLine_C3;
} LeftLine_Parameter;

#endif

#ifndef DEFINED_TYPEDEF_FOR_Right_LaneParameter_
#define DEFINED_TYPEDEF_FOR_Right_LaneParameter_

typedef struct {
  real_T RightLine_Exist_Probability;
  uint8_T RightLine_Type_Class;
  real_T RightLine_ViewRange_Start;
  real_T RightLine_ViewRange_end;
  real_T RightLine_C0;
  real_T RightLine_C1;
  real_T RightLine_C2;
  real_T RightLine_C3;
} Right_LaneParameter;

#endif

#ifndef DEFINED_TYPEDEF_FOR_LQR_Error_
#define DEFINED_TYPEDEF_FOR_LQR_Error_

typedef struct {
  real32_T LQR_lateral_error;
  real32_T LQR_lateral_error_rate;
  real32_T LQR_heading_error;
  real32_T LQR_heading_error_rate;
  real32_T LQR_Curve;
} LQR_Error;

#endif

#ifndef DEFINED_TYPEDEF_FOR_Center_LaneParameter_
#define DEFINED_TYPEDEF_FOR_Center_LaneParameter_

typedef struct {
  real32_T Center_Curvature;
  real32_T Center_CurvatureDerivative;
  real32_T Center_HeadingAngle;
  real32_T Center_LateralOffset;
  uint8_T Center_quality;
} Center_LaneParameter;

#endif

#ifndef DEFINED_TYPEDEF_FOR_VehPropertyBus_
#define DEFINED_TYPEDEF_FOR_VehPropertyBus_

typedef struct {
  real32_T VehicleMass;
  real32_T FrontCorneringStiffness;
  real32_T RearCorneringStiffness;
  real32_T FrontAxleLength;
  real32_T RearAxleLength;
  real32_T Inertia;
  real32_T VehicleWidth;
  real32_T VehicleSteeringWheelRatio;
} VehPropertyBus;

#endif

#ifndef DEFINED_TYPEDEF_FOR_LDP_Traj_
#define DEFINED_TYPEDEF_FOR_LDP_Traj_

typedef struct {
  real32_T LDP_LatOffset;
  real32_T LDP_Heading;
  real32_T LDP_Curvature;
  boolean_T IsLDPTrajFoundError;
} LDP_Traj;

#endif

#ifndef DEFINED_TYPEDEF_FOR_ILC_Traj_
#define DEFINED_TYPEDEF_FOR_ILC_Traj_

typedef struct {
  real32_T ILC_LatOffset;
  real32_T ILC_Heading;
  real32_T ILC_Curvature;
  real32_T ILC_CurvatureRate;
  boolean_T IsILCTrajFoundError;
} ILC_Traj;

#endif

#ifndef DEFINED_TYPEDEF_FOR_Ideal_Path_
#define DEFINED_TYPEDEF_FOR_Ideal_Path_

typedef struct {
  real32_T Ideal_Path_c0;
  real32_T Ideal_Path_c1;
  real32_T Ideal_Path_c2;
  real32_T Ideal_Path_c3;
} Ideal_Path;

#endif

#ifndef DEFINED_TYPEDEF_FOR_ILC_StatusManager_
#define DEFINED_TYPEDEF_FOR_ILC_StatusManager_

typedef struct {
  /* ilc function status */
  uint8_T ILC_Status;

  /* ilc trigger direction */
  uint8_T index_ILCDir;
} ILC_StatusManager;

#endif

#ifndef DEFINED_TYPEDEF_FOR_LDWWarningManager_
#define DEFINED_TYPEDEF_FOR_LDWWarningManager_

typedef struct {
  uint8_T WarnMgrSts;
  uint8_T WarnSide;
  real32_T WarnDuration;
} LDWWarningManager;

#endif

#ifndef DEFINED_TYPEDEF_FOR_Roadedge_Parameter_
#define DEFINED_TYPEDEF_FOR_Roadedge_Parameter_

typedef struct {
  real32_T Roadedge_ExistProb;
  uint8_T Roadedge_Type;
  real32_T Roadedge_DxStart;
  real32_T Roadedge_DxEnd;
  real32_T Roadedge_Dy;
  real32_T Roadedge_HeadingAngle;
  real32_T Roadedge_Curv;
  real32_T Roadedge_CurvChange;
} Roadedge_Parameter;

#endif

#ifndef DEFINED_TYPEDEF_FOR_TargetLane_Parameter_
#define DEFINED_TYPEDEF_FOR_TargetLane_Parameter_

typedef struct {
  real32_T LateralOffset;
  real32_T HeadingAngle;
  real32_T Curvature;
  real32_T CurvatureDerivative;
  uint8_T Quality;
} TargetLane_Parameter;

#endif

#ifndef DEFINED_TYPEDEF_FOR_struct_61nmPlKlXGwX2JfFXgo6CF_
#define DEFINED_TYPEDEF_FOR_struct_61nmPlKlXGwX2JfFXgo6CF_

typedef struct {
  real32_T ILC_path_a0;
  real32_T ILC_path_a1;
  real32_T ILC_path_a2;
  real32_T ILC_path_a3;
  real32_T ILC_path_a4;
  real32_T ILC_path_a5;
} struct_61nmPlKlXGwX2JfFXgo6CF;

#endif

#ifndef DEFINED_TYPEDEF_FOR_struct_5oFbKCH9MfU2rMOUvvtsjH_
#define DEFINED_TYPEDEF_FOR_struct_5oFbKCH9MfU2rMOUvvtsjH_

typedef struct {
  real32_T NextStepLgt;
  real32_T NextStepLat;
  real32_T NextStepHdg;
  real32_T NextStepCurv;
} struct_5oFbKCH9MfU2rMOUvvtsjH;

#endif

/* Forward declaration for rtModel */
typedef struct tag_RTM_ArcAdas_EmbeddedCoder_T RT_MODEL_ArcAdas_EmbeddedCode_T;

#endif                     /* RTW_HEADER_ArcAdas_EmbeddedCoder_Frame_types_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
