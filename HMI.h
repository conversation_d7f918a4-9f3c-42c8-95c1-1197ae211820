#ifndef HMI_H_
#define HMI_H_

#include "arcsoft_adas_connection_SL.h"

/*CAM CIPV INFO*/
extern uint8_t cc_CIPV_ID;
extern float   cc_CIPV_LongDistance;
extern float   cc_CIPV_AbsLongVel;
extern float   cc_CIPV_LatDistance;
extern uint8_t cc_CIPV_ObjType;
extern float   cc_CIPV_LatVel;  
extern float   cc_CIPV_LongAccel;  
extern uint8_t cc_CIPV_LocIndex; 

/*HMI*/
extern uint8_t cc_MainObjID;
extern uint8_t cc_Obj1_Type;
extern float   cc_Obj1_LatDis;
extern float   cc_Obj1_LongDis;
extern uint8_t cc_Obj2_Type;
extern float   cc_Obj2_LatDis;
extern float   cc_Obj2_LongDis;
extern uint8_t cc_Obj3_Type;
extern float   cc_Obj3_LatDis;
extern float   cc_Obj3_LongDis;
extern uint8_t cc_Obj4_Type;
extern float   cc_Obj4_LatDis;
extern float   cc_Obj4_LongDis;
extern uint8_t cc_Obj5_Type;
extern float   cc_Obj5_LatDis;
extern float   cc_Obj5_LongDis;
extern uint8_t cc_Obj6_Type;
extern float   cc_Obj6_LatDis;
extern float   cc_Obj6_LongDis;
extern uint8_t cc_Obj7_Type;
extern float   cc_Obj7_LatDis;
extern float   cc_Obj7_LongDis;
extern uint8_t cc_Obj8_Type;
extern float   cc_Obj8_LatDis;
extern float   cc_Obj8_LongDis;
extern uint8_t cc_Obj9_Type;
extern float   cc_Obj9_LatDis;
extern float   cc_Obj9_LongDis;

/*LD*/
extern uint8_t cc_LeftLaneColor;
extern uint8_t cc_LeftLaneType;
extern uint8_t cc_LeftLeftLaneColor;
extern uint8_t cc_LeftLeftLaneType;	
extern uint8_t cc_RightLaneColor;
extern uint8_t cc_RightLaneType;
extern uint8_t cc_RightRightLaneColor;
extern uint8_t cc_RightRightLaneType;
extern float   cc_LeftLane_C0;
extern float   cc_LeftLane_C1;
extern float   cc_LeftLane_C2;
extern float   cc_LeftLane_C3;
extern float   cc_LeftLeftLane_C0;
extern float   cc_LeftLeftLane_C1;
extern float   cc_LeftLeftLane_C2;
extern float   cc_LeftLeftLane_C3;	
extern float   cc_RightLane_C0;
extern float   cc_RightLane_C1;
extern float   cc_RightLane_C2;
extern float   cc_RightLane_C3;
extern float   cc_RightRightLane_C0;
extern float   cc_RightRightLane_C1;
extern float   cc_RightRightLane_C2;
extern float   cc_RightRightLane_C3;


extern void HMI_Task(void);


#endif /* HMI_H_ */

